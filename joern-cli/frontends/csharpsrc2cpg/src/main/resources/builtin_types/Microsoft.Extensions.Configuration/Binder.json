{"Microsoft.CodeAnalysis": [{"name": "Microsoft.CodeAnalysis.EmbeddedAttribute", "methods": [], "fields": []}], "System.Runtime.CompilerServices": [{"name": "System.Runtime.CompilerServices.NullableAttribute", "methods": [], "fields": []}, {"name": "System.Runtime.CompilerServices.NullableContextAttribute", "methods": [], "fields": []}, {"name": "System.Runtime.CompilerServices.NullablePublicOnlyAttribute", "methods": [], "fields": []}, {"name": "System.Runtime.CompilerServices.RefSafetyRulesAttribute", "methods": [], "fields": []}, {"name": "System.Runtime.CompilerServices.CallerArgumentExpressionAttribute", "methods": [{"name": "get_ParameterName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}], "FxResources.Microsoft.Extensions.Configuration.Binder": [{"name": "FxResources.Microsoft.Extensions.Configuration.Binder.SR", "methods": [], "fields": []}], "System": [{"name": "System.ThrowHelper", "methods": [{"name": "IfNullOrWhitespace", "returnType": "System.String", "parameterTypes": [["argument", "System.String"], ["paramName", "System.String"]], "isStatic": true}], "fields": []}, {"name": "System.SR", "methods": [], "fields": []}], "System.Diagnostics.CodeAnalysis": [{"name": "System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute", "methods": [{"name": "get_MemberTypes", "returnType": "System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute", "methods": [{"name": "get_Message", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Url", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute", "methods": [{"name": "get_Category", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_CheckId", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Scope", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Scope", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Target", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Target", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_MessageId", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_MessageId", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Justification", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Justification", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute", "methods": [{"name": "get_Message", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Url", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.AllowNullAttribute", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.DisallowNullAttribute", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.MaybeNullAttribute", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.NotNullAttribute", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute", "methods": [{"name": "get_ReturnValue", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.NotNullWhenAttribute", "methods": [{"name": "get_ReturnValue", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute", "methods": [{"name": "get_ParameterName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute", "methods": [{"name": "get_ParameterValue", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.MemberNotNullAttribute", "methods": [{"name": "get_Members", "returnType": "System.String[]", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute", "methods": [{"name": "get_ReturnValue", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "get_Members", "returnType": "System.String[]", "parameterTypes": [], "isStatic": false}], "fields": []}], "System.Runtime.InteropServices": [{"name": "System.Runtime.InteropServices.LibraryImportAttribute", "methods": [{"name": "get_LibraryName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_EntryPoint", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_EntryPoint", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_StringMarshalling", "returnType": "System.Runtime.InteropServices.StringMarshalling", "parameterTypes": [], "isStatic": false}, {"name": "set_StringMarshalling", "returnType": "System.Void", "parameterTypes": [["value", "System.Runtime.InteropServices.StringMarshalling"]], "isStatic": false}, {"name": "get_StringMarshallingCustomType", "returnType": "System.Type", "parameterTypes": [], "isStatic": false}, {"name": "set_StringMarshallingCustomType", "returnType": "System.Void", "parameterTypes": [["value", "System.Type"]], "isStatic": false}, {"name": "get_SetLastError", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_SetLastError", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}], "fields": []}, {"name": "System.Runtime.InteropServices.StringMarshalling", "methods": [], "fields": []}], "Microsoft.Extensions.Internal": [{"name": "Microsoft.Extensions.Internal.ParameterDefaultValue", "methods": [{"name": "TryGetDefaultValue", "returnType": "System.Boolean", "parameterTypes": [["parameter", "System.Reflection.ParameterInfo"], ["defaultValue", "System.Object&"]], "isStatic": true}, {"name": "CheckHasDefaultValue", "returnType": "System.Boolean", "parameterTypes": [["parameter", "System.Reflection.ParameterInfo"], ["tryToGetDefaultValue", "System.Boolean&"]], "isStatic": true}], "fields": []}], "Microsoft.Extensions.Configuration": [{"name": "Microsoft.Extensions.Configuration.BinderOptions", "methods": [{"name": "get_BindNonPublicProperties", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_BindNonPublicProperties", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_ErrorOnUnknownConfiguration", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_ErrorOnUnknownConfiguration", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.BindingPoint", "methods": [{"name": "get_IsReadOnly", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "get_HasNew<PERSON><PERSON>ue", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "get_Value", "returnType": "System.Object", "parameterTypes": [], "isStatic": false}, {"name": "SetValue", "returnType": "System.Void", "parameterTypes": [["newValue", "System.Object"]], "isStatic": false}, {"name": "TrySetValue", "returnType": "System.Void", "parameterTypes": [["newValue", "System.Object"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ConfigurationBinder", "methods": [{"name": "Get", "returnType": "T", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"]], "isStatic": true}, {"name": "Get", "returnType": "T", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"], ["configureOptions", "System.Action`1<Microsoft.Extensions.Configuration.BinderOptions>"]], "isStatic": true}, {"name": "Get", "returnType": "System.Object", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"], ["type", "System.Type"]], "isStatic": true}, {"name": "Get", "returnType": "System.Object", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"], ["type", "System.Type"], ["configureOptions", "System.Action`1<Microsoft.Extensions.Configuration.BinderOptions>"]], "isStatic": true}, {"name": "Bind", "returnType": "System.Void", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"], ["key", "System.String"], ["instance", "System.Object"]], "isStatic": true}, {"name": "Bind", "returnType": "System.Void", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"], ["instance", "System.Object"]], "isStatic": true}, {"name": "Bind", "returnType": "System.Void", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"], ["instance", "System.Object"], ["configureOptions", "System.Action`1<Microsoft.Extensions.Configuration.BinderOptions>"]], "isStatic": true}, {"name": "GetValue", "returnType": "T", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"], ["key", "System.String"]], "isStatic": true}, {"name": "GetValue", "returnType": "T", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"], ["key", "System.String"], ["defaultValue", "T"]], "isStatic": true}, {"name": "GetValue", "returnType": "System.Object", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"], ["type", "System.Type"], ["key", "System.String"]], "isStatic": true}, {"name": "GetValue", "returnType": "System.Object", "parameterTypes": [["configuration", "Microsoft.Extensions.Configuration.IConfiguration"], ["type", "System.Type"], ["key", "System.String"], ["defaultValue", "System.Object"]], "isStatic": true}], "fields": []}]}