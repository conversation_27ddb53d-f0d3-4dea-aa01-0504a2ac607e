{"Microsoft.CodeAnalysis": [{"name": "Microsoft.CodeAnalysis.EmbeddedAttribute", "methods": [], "fields": []}], "System.Runtime.CompilerServices": [{"name": "System.Runtime.CompilerServices.NullableAttribute", "methods": [], "fields": []}, {"name": "System.Runtime.CompilerServices.NullableContextAttribute", "methods": [], "fields": []}, {"name": "System.Runtime.CompilerServices.NullablePublicOnlyAttribute", "methods": [], "fields": []}, {"name": "System.Runtime.CompilerServices.RefSafetyRulesAttribute", "methods": [], "fields": []}, {"name": "System.Runtime.CompilerServices.CallerArgumentExpressionAttribute", "methods": [{"name": "get_ParameterName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}], "FxResources.Microsoft.Extensions.Configuration": [{"name": "FxResources.Microsoft.Extensions.Configuration.SR", "methods": [], "fields": []}], "System": [{"name": "System.ThrowHelper", "methods": [{"name": "IfNullOrWhitespace", "returnType": "System.String", "parameterTypes": [["argument", "System.String"], ["paramName", "System.String"]], "isStatic": true}], "fields": []}, {"name": "System.SR", "methods": [], "fields": []}], "System.Diagnostics.CodeAnalysis": [{"name": "System.Diagnostics.CodeAnalysis.AllowNullAttribute", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.DisallowNullAttribute", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.MaybeNullAttribute", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.NotNullAttribute", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute", "methods": [{"name": "get_ReturnValue", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.NotNullWhenAttribute", "methods": [{"name": "get_ReturnValue", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute", "methods": [{"name": "get_ParameterName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute", "methods": [], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute", "methods": [{"name": "get_ParameterValue", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.MemberNotNullAttribute", "methods": [{"name": "get_Members", "returnType": "System.String[]", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute", "methods": [{"name": "get_ReturnValue", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "get_Members", "returnType": "System.String[]", "parameterTypes": [], "isStatic": false}], "fields": []}], "System.Runtime.InteropServices": [{"name": "System.Runtime.InteropServices.LibraryImportAttribute", "methods": [{"name": "get_LibraryName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_EntryPoint", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_EntryPoint", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_StringMarshalling", "returnType": "System.Runtime.InteropServices.StringMarshalling", "parameterTypes": [], "isStatic": false}, {"name": "set_StringMarshalling", "returnType": "System.Void", "parameterTypes": [["value", "System.Runtime.InteropServices.StringMarshalling"]], "isStatic": false}, {"name": "get_StringMarshallingCustomType", "returnType": "System.Type", "parameterTypes": [], "isStatic": false}, {"name": "set_StringMarshallingCustomType", "returnType": "System.Void", "parameterTypes": [["value", "System.Type"]], "isStatic": false}, {"name": "get_SetLastError", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_SetLastError", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}], "fields": []}, {"name": "System.Runtime.InteropServices.StringMarshalling", "methods": [], "fields": []}], "Microsoft.Extensions.FileProviders": [{"name": "Microsoft.Extensions.FileProviders.EmptyDisposable", "methods": [{"name": "get_Instance", "returnType": "Microsoft.Extensions.FileProviders.EmptyDisposable", "parameterTypes": [], "isStatic": true}, {"name": "Dispose", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.Extensions.Internal": [{"name": "Microsoft.Extensions.Internal.ChangeCallbackRegistrar", "methods": [], "fields": []}], "Microsoft.Extensions.Configuration": [{"name": "Microsoft.Extensions.Configuration.ChainedBuilderExtensions", "methods": [{"name": "AddConfiguration", "returnType": "Microsoft.Extensions.Configuration.IConfigurationBuilder", "parameterTypes": [["configurationBuilder", "Microsoft.Extensions.Configuration.IConfigurationBuilder"], ["config", "Microsoft.Extensions.Configuration.IConfiguration"]], "isStatic": true}, {"name": "AddConfiguration", "returnType": "Microsoft.Extensions.Configuration.IConfigurationBuilder", "parameterTypes": [["configurationBuilder", "Microsoft.Extensions.Configuration.IConfigurationBuilder"], ["config", "Microsoft.Extensions.Configuration.IConfiguration"], ["shouldDisposeConfiguration", "System.Boolean"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ChainedConfigurationProvider", "methods": [{"name": "get_Configuration", "returnType": "Microsoft.Extensions.Configuration.IConfiguration", "parameterTypes": [], "isStatic": false}, {"name": "TryGet", "returnType": "System.Boolean", "parameterTypes": [["key", "System.String"], ["value", "System.String&"]], "isStatic": false}, {"name": "Set", "returnType": "System.Void", "parameterTypes": [["key", "System.String"], ["value", "System.String"]], "isStatic": false}, {"name": "GetReloadToken", "returnType": "Microsoft.Extensions.Primitives.IChangeToken", "parameterTypes": [], "isStatic": false}, {"name": "Load", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Collections.Generic.IEnumerable`1<System.String>", "parameterTypes": [["earlierKeys", "System.Collections.Generic.IEnumerable`1<System.String>"], ["parentPath", "System.String"]], "isStatic": false}, {"name": "Dispose", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ChainedConfigurationSource", "methods": [{"name": "get_Configuration", "returnType": "Microsoft.Extensions.Configuration.IConfiguration", "parameterTypes": [], "isStatic": false}, {"name": "set_Configuration", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.Extensions.Configuration.IConfiguration"]], "isStatic": false}, {"name": "get_ShouldDisposeConfiguration", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_ShouldDisposeConfiguration", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "Build", "returnType": "Microsoft.Extensions.Configuration.IConfigurationProvider", "parameterTypes": [["builder", "Microsoft.Extensions.Configuration.IConfigurationBuilder"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ConfigurationBuilder", "methods": [{"name": "get_Sources", "returnType": "System.Collections.Generic.IList`1<Microsoft.Extensions.Configuration.IConfigurationSource>", "parameterTypes": [], "isStatic": false}, {"name": "get_Properties", "returnType": "System.Collections.Generic.IDictionary`2<System.String,System.Object>", "parameterTypes": [], "isStatic": false}, {"name": "Add", "returnType": "Microsoft.Extensions.Configuration.IConfigurationBuilder", "parameterTypes": [["source", "Microsoft.Extensions.Configuration.IConfigurationSource"]], "isStatic": false}, {"name": "Build", "returnType": "Microsoft.Extensions.Configuration.IConfigurationRoot", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ConfigurationKeyComparer", "methods": [{"name": "get_Instance", "returnType": "Microsoft.Extensions.Configuration.ConfigurationKeyComparer", "parameterTypes": [], "isStatic": true}, {"name": "Compare", "returnType": "System.Int32", "parameterTypes": [["x", "System.String"], ["y", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ConfigurationManager", "methods": [{"name": "get_Item", "returnType": "System.String", "parameterTypes": [["key", "System.String"]], "isStatic": false}, {"name": "set_Item", "returnType": "System.Void", "parameterTypes": [["key", "System.String"], ["value", "System.String"]], "isStatic": false}, {"name": "GetSection", "returnType": "Microsoft.Extensions.Configuration.IConfigurationSection", "parameterTypes": [["key", "System.String"]], "isStatic": false}, {"name": "GetChildren", "returnType": "System.Collections.Generic.IEnumerable`1<Microsoft.Extensions.Configuration.IConfigurationSection>", "parameterTypes": [], "isStatic": false}, {"name": "get_Sources", "returnType": "System.Collections.Generic.IList`1<Microsoft.Extensions.Configuration.IConfigurationSource>", "parameterTypes": [], "isStatic": false}, {"name": "Dispose", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ConfigurationProvider", "methods": [{"name": "TryGet", "returnType": "System.Boolean", "parameterTypes": [["key", "System.String"], ["value", "System.String&"]], "isStatic": false}, {"name": "Set", "returnType": "System.Void", "parameterTypes": [["key", "System.String"], ["value", "System.String"]], "isStatic": false}, {"name": "Load", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Collections.Generic.IEnumerable`1<System.String>", "parameterTypes": [["earlierKeys", "System.Collections.Generic.IEnumerable`1<System.String>"], ["parentPath", "System.String"]], "isStatic": false}, {"name": "GetReloadToken", "returnType": "Microsoft.Extensions.Primitives.IChangeToken", "parameterTypes": [], "isStatic": false}, {"name": "ToString", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ConfigurationReloadToken", "methods": [{"name": "get_ActiveChangeCallbacks", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON><PERSON>ed", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "RegisterChangeCallback", "returnType": "System.IDisposable", "parameterTypes": [["callback", "System.Action`1<System.Object>"], ["state", "System.Object"]], "isStatic": false}, {"name": "OnReload", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ConfigurationRoot", "methods": [{"name": "get_Providers", "returnType": "System.Collections.Generic.IEnumerable`1<Microsoft.Extensions.Configuration.IConfigurationProvider>", "parameterTypes": [], "isStatic": false}, {"name": "get_Item", "returnType": "System.String", "parameterTypes": [["key", "System.String"]], "isStatic": false}, {"name": "set_Item", "returnType": "System.Void", "parameterTypes": [["key", "System.String"], ["value", "System.String"]], "isStatic": false}, {"name": "GetChildren", "returnType": "System.Collections.Generic.IEnumerable`1<Microsoft.Extensions.Configuration.IConfigurationSection>", "parameterTypes": [], "isStatic": false}, {"name": "GetReloadToken", "returnType": "Microsoft.Extensions.Primitives.IChangeToken", "parameterTypes": [], "isStatic": false}, {"name": "GetSection", "returnType": "Microsoft.Extensions.Configuration.IConfigurationSection", "parameterTypes": [["key", "System.String"]], "isStatic": false}, {"name": "Reload", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "Dispose", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ConfigurationSection", "methods": [{"name": "get_Path", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Key", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Value", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Value", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Item", "returnType": "System.String", "parameterTypes": [["key", "System.String"]], "isStatic": false}, {"name": "set_Item", "returnType": "System.Void", "parameterTypes": [["key", "System.String"], ["value", "System.String"]], "isStatic": false}, {"name": "GetSection", "returnType": "Microsoft.Extensions.Configuration.IConfigurationSection", "parameterTypes": [["key", "System.String"]], "isStatic": false}, {"name": "GetChildren", "returnType": "System.Collections.Generic.IEnumerable`1<Microsoft.Extensions.Configuration.IConfigurationSection>", "parameterTypes": [], "isStatic": false}, {"name": "GetReloadToken", "returnType": "Microsoft.Extensions.Primitives.IChangeToken", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ConfigurationSectionDebugView", "methods": [{"name": "get_Path", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Key", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_FullPath", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Value", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Provider", "returnType": "Microsoft.Extensions.Configuration.IConfigurationProvider", "parameterTypes": [], "isStatic": false}, {"name": "ToString", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.InternalConfigurationRootExtensions", "methods": [], "fields": []}, {"name": "Microsoft.Extensions.Configuration.MemoryConfigurationBuilderExtensions", "methods": [{"name": "AddInMemoryCollection", "returnType": "Microsoft.Extensions.Configuration.IConfigurationBuilder", "parameterTypes": [["configurationBuilder", "Microsoft.Extensions.Configuration.IConfigurationBuilder"]], "isStatic": true}, {"name": "AddInMemoryCollection", "returnType": "Microsoft.Extensions.Configuration.IConfigurationBuilder", "parameterTypes": [["configurationBuilder", "Microsoft.Extensions.Configuration.IConfigurationBuilder"], ["initialData", "System.Collections.Generic.IEnumerable`1<System.Collections.Generic.KeyValuePair`2<System.String,System.String>>"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ReferenceCountedProviders", "methods": [{"name": "Create", "returnType": "Microsoft.Extensions.Configuration.ReferenceCountedProviders", "parameterTypes": [["providers", "System.Collections.Generic.List`1<Microsoft.Extensions.Configuration.IConfigurationProvider>"]], "isStatic": true}, {"name": "CreateDisposed", "returnType": "Microsoft.Extensions.Configuration.ReferenceCountedProviders", "parameterTypes": [["providers", "System.Collections.Generic.List`1<Microsoft.Extensions.Configuration.IConfigurationProvider>"]], "isStatic": true}, {"name": "get_Providers", "returnType": "System.Collections.Generic.List`1<Microsoft.Extensions.Configuration.IConfigurationProvider>", "parameterTypes": [], "isStatic": false}, {"name": "set_Providers", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.List`1<Microsoft.Extensions.Configuration.IConfigurationProvider>"]], "isStatic": false}, {"name": "get_NonReferenceCountedProviders", "returnType": "System.Collections.Generic.List`1<Microsoft.Extensions.Configuration.IConfigurationProvider>", "parameterTypes": [], "isStatic": false}, {"name": "AddReference", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "Dispose", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.ReferenceCountedProviderManager", "methods": [{"name": "get_NonReferenceCountedProviders", "returnType": "System.Collections.Generic.IEnumerable`1<Microsoft.Extensions.Configuration.IConfigurationProvider>", "parameterTypes": [], "isStatic": false}, {"name": "GetReference", "returnType": "Microsoft.Extensions.Configuration.ReferenceCountedProviders", "parameterTypes": [], "isStatic": false}, {"name": "ReplaceProviders", "returnType": "System.Void", "parameterTypes": [["providers", "System.Collections.Generic.List`1<Microsoft.Extensions.Configuration.IConfigurationProvider>"]], "isStatic": false}, {"name": "AddProvider", "returnType": "System.Void", "parameterTypes": [["provider", "Microsoft.Extensions.Configuration.IConfigurationProvider"]], "isStatic": false}, {"name": "Dispose", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.StreamConfigurationProvider", "methods": [{"name": "get_Source", "returnType": "Microsoft.Extensions.Configuration.StreamConfigurationSource", "parameterTypes": [], "isStatic": false}, {"name": "Load", "returnType": "System.Void", "parameterTypes": [["stream", "System.IO.Stream"]], "isStatic": false}, {"name": "Load", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.StreamConfigurationSource", "methods": [{"name": "get_Stream", "returnType": "System.IO.Stream", "parameterTypes": [], "isStatic": false}, {"name": "set_Stream", "returnType": "System.Void", "parameterTypes": [["value", "System.IO.Stream"]], "isStatic": false}, {"name": "Build", "returnType": "Microsoft.Extensions.Configuration.IConfigurationProvider", "parameterTypes": [["builder", "Microsoft.Extensions.Configuration.IConfigurationBuilder"]], "isStatic": false}], "fields": []}], "Microsoft.Extensions.Configuration.Memory": [{"name": "Microsoft.Extensions.Configuration.Memory.MemoryConfigurationProvider", "methods": [{"name": "Add", "returnType": "System.Void", "parameterTypes": [["key", "System.String"], ["value", "System.String"]], "isStatic": false}, {"name": "GetEnumerator", "returnType": "System.Collections.Generic.IEnumerator`1<System.Collections.Generic.KeyValuePair`2<System.String,System.String>>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Configuration.Memory.MemoryConfigurationSource", "methods": [{"name": "get_InitialData", "returnType": "System.Collections.Generic.IEnumerable`1<System.Collections.Generic.KeyValuePair`2<System.String,System.String>>", "parameterTypes": [], "isStatic": false}, {"name": "set_InitialData", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IEnumerable`1<System.Collections.Generic.KeyValuePair`2<System.String,System.String>>"]], "isStatic": false}, {"name": "Build", "returnType": "Microsoft.Extensions.Configuration.IConfigurationProvider", "parameterTypes": [["builder", "Microsoft.Extensions.Configuration.IConfigurationBuilder"]], "isStatic": false}], "fields": []}]}