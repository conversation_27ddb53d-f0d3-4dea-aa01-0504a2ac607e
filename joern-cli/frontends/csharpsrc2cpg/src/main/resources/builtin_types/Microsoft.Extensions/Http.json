{"FxResources.Microsoft.Extensions.Http": [{"name": "FxResources.Microsoft.Extensions.Http.SR", "methods": [], "fields": []}], "System": [{"name": "System.ThrowHelper", "methods": [{"name": "IfNullOrWhitespace", "returnType": "System.String", "parameterTypes": [["argument", "System.String"], ["paramName", "System.String"]], "isStatic": true}], "fields": []}, {"name": "System.SR", "methods": [], "fields": []}], "System.Net.Http": [{"name": "System.Net.Http.HttpClientFactoryExtensions", "methods": [{"name": "CreateClient", "returnType": "System.Net.Http.HttpClient", "parameterTypes": [["factory", "System.Net.Http.IHttpClientFactory"]], "isStatic": true}], "fields": []}, {"name": "System.Net.Http.HttpMessageHandlerFactoryExtensions", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Net.Http.HttpMessageHandler", "parameterTypes": [["factory", "System.Net.Http.IHttpMessageHandlerFactory"]], "isStatic": true}], "fields": []}, {"name": "System.Net.Http.IHttpClientFactory", "methods": [{"name": "CreateClient", "returnType": "System.Net.Http.HttpClient", "parameterTypes": [["name", "System.String"]], "isStatic": false}], "fields": []}, {"name": "System.Net.Http.IHttpMessageHandlerFactory", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Net.Http.HttpMessageHandler", "parameterTypes": [["name", "System.String"]], "isStatic": false}], "fields": []}], "Microsoft.Extensions.Internal": [{"name": "Microsoft.Extensions.Internal.ValueStopwatch", "methods": [{"name": "get_IsActive", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "StartNew", "returnType": "Microsoft.Extensions.Internal.ValueStopwatch", "parameterTypes": [], "isStatic": true}, {"name": "GetElapsedTime", "returnType": "System.TimeSpan", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Internal.NonCapturingTimer", "methods": [{"name": "Create", "returnType": "System.Threading.Timer", "parameterTypes": [["callback", "System.Threading.TimerCallback"], ["state", "System.Object"], ["dueTime", "System.TimeSpan"], ["period", "System.TimeSpan"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.Extensions.Internal.TypeNameHelper", "methods": [{"name": "GetTypeDisplayName", "returnType": "System.String", "parameterTypes": [["item", "System.Object"], ["fullName", "System.Boolean"]], "isStatic": true}, {"name": "GetTypeDisplayName", "returnType": "System.String", "parameterTypes": [["type", "System.Type"], ["fullName", "System.Boolean"], ["includeGenericParameterNames", "System.Boolean"], ["includeGenericParameters", "System.Boolean"], ["nestedType<PERSON><PERSON><PERSON><PERSON>", "System.Char"]], "isStatic": true}], "fields": []}], "Microsoft.Extensions.DependencyInjection": [{"name": "Microsoft.Extensions.DependencyInjection.DefaultHttpClientBuilder", "methods": [{"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Services", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceCollection", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.DependencyInjection.DefaultHttpClientBuilderServiceCollection", "methods": [{"name": "Add", "returnType": "System.Void", "parameterTypes": [["item", "Microsoft.Extensions.DependencyInjection.ServiceDescriptor"]], "isStatic": false}, {"name": "get_Item", "returnType": "Microsoft.Extensions.DependencyInjection.ServiceDescriptor", "parameterTypes": [["index", "System.Int32"]], "isStatic": false}, {"name": "set_Item", "returnType": "System.Void", "parameterTypes": [["index", "System.Int32"], ["value", "Microsoft.Extensions.DependencyInjection.ServiceDescriptor"]], "isStatic": false}, {"name": "get_Count", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "get_IsReadOnly", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "Clear", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "Contains", "returnType": "System.Boolean", "parameterTypes": [["item", "Microsoft.Extensions.DependencyInjection.ServiceDescriptor"]], "isStatic": false}, {"name": "CopyTo", "returnType": "System.Void", "parameterTypes": [["array", "Microsoft.Extensions.DependencyInjection.ServiceDescriptor[]"], ["arrayIndex", "System.Int32"]], "isStatic": false}, {"name": "GetEnumerator", "returnType": "System.Collections.Generic.IEnumerator`1<Microsoft.Extensions.DependencyInjection.ServiceDescriptor>", "parameterTypes": [], "isStatic": false}, {"name": "IndexOf", "returnType": "System.Int32", "parameterTypes": [["item", "Microsoft.Extensions.DependencyInjection.ServiceDescriptor"]], "isStatic": false}, {"name": "Insert", "returnType": "System.Void", "parameterTypes": [["index", "System.Int32"], ["item", "Microsoft.Extensions.DependencyInjection.ServiceDescriptor"]], "isStatic": false}, {"name": "Remove", "returnType": "System.Boolean", "parameterTypes": [["item", "Microsoft.Extensions.DependencyInjection.ServiceDescriptor"]], "isStatic": false}, {"name": "RemoveAt", "returnType": "System.Void", "parameterTypes": [["index", "System.Int32"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.DependencyInjection.DefaultHttpClientConfigurationTracker", "methods": [{"name": "get_InsertDefaultsAfterDescriptor", "returnType": "Microsoft.Extensions.DependencyInjection.ServiceDescriptor", "parameterTypes": [], "isStatic": false}, {"name": "set_InsertDefaultsAfterDescriptor", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.Extensions.DependencyInjection.ServiceDescriptor"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.DependencyInjection.DefaultSocketsHttpHandlerBuilder", "methods": [{"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Services", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceCollection", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.DependencyInjection.HttpClientBuilderExtensions", "methods": [{"name": "ConfigureHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configureClient", "System.Action`1<System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "ConfigureHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configureClient", "System.Action`2<System.IServiceProvider,System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpMessageHandler", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configure<PERSON><PERSON><PERSON>", "System.Func`1<System.Net.Http.DelegatingHandler>"]], "isStatic": true}, {"name": "AddHttpMessageHandler", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configure<PERSON><PERSON><PERSON>", "System.Func`2<System.IServiceProvider,System.Net.Http.DelegatingHandler>"]], "isStatic": true}, {"name": "AddHttpMessageHandler", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"]], "isStatic": true}, {"name": "ConfigurePrimaryHttpMessageHandler", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configure<PERSON><PERSON><PERSON>", "System.Func`1<System.Net.Http.HttpMessageHandler>"]], "isStatic": true}, {"name": "ConfigurePrimaryHttpMessageHandler", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configure<PERSON><PERSON><PERSON>", "System.Func`2<System.IServiceProvider,System.Net.Http.HttpMessageHandler>"]], "isStatic": true}, {"name": "ConfigurePrimaryHttpMessageHandler", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"]], "isStatic": true}, {"name": "ConfigurePrimaryHttpMessageHandler", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configure<PERSON><PERSON><PERSON>", "System.Action`2<System.Net.Http.HttpMessage<PERSON><PERSON><PERSON>,System.IServiceProvider>"]], "isStatic": true}, {"name": "ConfigureHttpMessageHandlerBuilder", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configureBuilder", "System.Action`1<Microsoft.Extensions.Http.HttpMessageHandlerBuilder>"]], "isStatic": true}, {"name": "UseSocketsHttpHandler", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configure<PERSON><PERSON><PERSON>", "System.Action`2<System.Net.Http.SocketsHttp<PERSON><PERSON><PERSON>,System.IServiceProvider>"]], "isStatic": true}, {"name": "UseSocketsHttpHandler", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configureBuilder", "System.Action`1<Microsoft.Extensions.DependencyInjection.ISocketsHttpHandlerBuilder>"]], "isStatic": true}, {"name": "AddTypedClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"]], "isStatic": true}, {"name": "AddTypedClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"]], "isStatic": true}, {"name": "AddTypedClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["factory", "System.Func`2<System.Net.Http.HttpClient,TClient>"]], "isStatic": true}, {"name": "AddTypedClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["factory", "System.Func`3<System.Net.Http.HttpClient,System.IServiceProvider,TClient>"]], "isStatic": true}, {"name": "RedactLoggedHeaders", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["shouldRedactHeaderValue", "System.Func`2<System.String,System.Boolean>"]], "isStatic": true}, {"name": "RedactLoggedHeaders", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["redactedLoggedHeaderNames", "System.Collections.Generic.IEnumerable`1<System.String>"]], "isStatic": true}, {"name": "SetHandlerLifetime", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["handlerLifetime", "System.TimeSpan"]], "isStatic": true}, {"name": "ConfigureAdditionalHttpMessageHandlers", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["configureAdditionalHandlers", "System.Action`2<System.Collections.Generic.IList`1<System.Net.Http.DelegatingHandler>,System.IServiceProvider>"]], "isStatic": true}, {"name": "<PERSON>d<PERSON><PERSON><PERSON>", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["httpClientLoggerFactory", "System.Func`2<System.IServiceProvider,Microsoft.Extensions.Http.Logging.IHttpClientLogger>"], ["wrapHandlersPipeline", "System.Boolean"]], "isStatic": true}, {"name": "<PERSON>d<PERSON><PERSON><PERSON>", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"], ["wrapHandlersPipeline", "System.Boolean"]], "isStatic": true}, {"name": "RemoveAllLoggers", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"]], "isStatic": true}, {"name": "AddDefaultLogger", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.Extensions.DependencyInjection.HttpClientFactoryServiceCollectionExtensions", "methods": [{"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceCollection", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"]], "isStatic": true}, {"name": "ConfigureHttpClientDefaults", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceCollection", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["configure", "System.Action`1<Microsoft.Extensions.DependencyInjection.IHttpClientBuilder>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"], ["configureClient", "System.Action`1<System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"], ["configureClient", "System.Action`2<System.IServiceProvider,System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["configureClient", "System.Action`1<System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["configureClient", "System.Action`2<System.IServiceProvider,System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["configureClient", "System.Action`1<System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["configureClient", "System.Action`2<System.IServiceProvider,System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"], ["configureClient", "System.Action`1<System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"], ["configureClient", "System.Action`2<System.IServiceProvider,System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"], ["configureClient", "System.Action`1<System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"], ["configureClient", "System.Action`2<System.IServiceProvider,System.Net.Http.HttpClient>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["factory", "System.Func`2<System.Net.Http.HttpClient,TImplementation>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"], ["factory", "System.Func`2<System.Net.Http.HttpClient,TImplementation>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["factory", "System.Func`3<System.Net.Http.HttpClient,System.IServiceProvider,TImplementation>"]], "isStatic": true}, {"name": "AddHttpClient", "returnType": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["name", "System.String"], ["factory", "System.Func`3<System.Net.Http.HttpClient,System.IServiceProvider,TImplementation>"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.Extensions.DependencyInjection.HttpClientMappingRegistry", "methods": [{"name": "get_NamedClientRegistrations", "returnType": "System.Collections.Generic.Dictionary`2<System.String,System.Type>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.DependencyInjection.IHttpClientBuilder", "methods": [{"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Services", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceCollection", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.DependencyInjection.ISocketsHttpHandlerBuilder", "methods": [{"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Services", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceCollection", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.DependencyInjection.SocketsHttpHandlerBuilderExtensions", "methods": [{"name": "Configure", "returnType": "Microsoft.Extensions.DependencyInjection.ISocketsHttpHandlerBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.ISocketsHttpHandlerBuilder"], ["configure", "System.Action`2<System.Net.Http.SocketsHttp<PERSON><PERSON><PERSON>,System.IServiceProvider>"]], "isStatic": true}, {"name": "Configure", "returnType": "Microsoft.Extensions.DependencyInjection.ISocketsHttpHandlerBuilder", "parameterTypes": [["builder", "Microsoft.Extensions.DependencyInjection.ISocketsHttpHandlerBuilder"], ["configuration", "Microsoft.Extensions.Configuration.IConfiguration"]], "isStatic": true}], "fields": []}], "Microsoft.Extensions.Http": [{"name": "Microsoft.Extensions.Http.ActiveHandlerTrackingEntry", "methods": [{"name": "get_Handler", "returnType": "Microsoft.Extensions.Http.LifetimeTrackingHttpMessageHandler", "parameterTypes": [], "isStatic": false}, {"name": "get_Lifetime", "returnType": "System.TimeSpan", "parameterTypes": [], "isStatic": false}, {"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Scope", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceScope", "parameterTypes": [], "isStatic": false}, {"name": "StartExpiryTimer", "returnType": "System.Void", "parameterTypes": [["callback", "System.Threading.TimerCallback"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.DefaultHttpClientFactory", "methods": [{"name": "CreateClient", "returnType": "System.Net.Http.HttpClient", "parameterTypes": [["name", "System.String"]], "isStatic": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Net.Http.HttpMessageHandler", "parameterTypes": [["name", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.DefaultHttpMessageHandlerBuilder", "methods": [{"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Name", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_<PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Net.Http.HttpMessageHandler", "parameterTypes": [], "isStatic": false}, {"name": "set_<PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Void", "parameterTypes": [["value", "System.Net.Http.HttpMessageHandler"]], "isStatic": false}, {"name": "get_AdditionalHandlers", "returnType": "System.Collections.Generic.IList`1<System.Net.Http.DelegatingHandler>", "parameterTypes": [], "isStatic": false}, {"name": "get_Services", "returnType": "System.IServiceProvider", "parameterTypes": [], "isStatic": false}, {"name": "Build", "returnType": "System.Net.Http.HttpMessageHandler", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.DefaultTypedHttpClientFactory`1", "methods": [{"name": "CreateClient", "returnType": "TClient", "parameterTypes": [["httpClient", "System.Net.Http.HttpClient"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.ExpiredHandlerTrackingEntry", "methods": [{"name": "get_CanDispose", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "get_InnerHandler", "returnType": "System.Net.Http.HttpMessageHandler", "parameterTypes": [], "isStatic": false}, {"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Scope", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceScope", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.HttpClientFactoryOptions", "methods": [{"name": "get_HttpMessageHandlerBuilderActions", "returnType": "System.Collections.Generic.IList`1<System.Action`1<Microsoft.Extensions.Http.HttpMessageHandlerBuilder>>", "parameterTypes": [], "isStatic": false}, {"name": "get_HttpClientActions", "returnType": "System.Collections.Generic.IList`1<System.Action`1<System.Net.Http.HttpClient>>", "parameterTypes": [], "isStatic": false}, {"name": "get_HandlerLifetime", "returnType": "System.TimeSpan", "parameterTypes": [], "isStatic": false}, {"name": "set_HandlerLifetime", "returnType": "System.Void", "parameterTypes": [["value", "System.TimeSpan"]], "isStatic": false}, {"name": "get_ShouldRedactHeaderValue", "returnType": "System.Func`2<System.String,System.Boolean>", "parameterTypes": [], "isStatic": false}, {"name": "set_ShouldRedactHeaderValue", "returnType": "System.Void", "parameterTypes": [["value", "System.Func`2<System.String,System.Boolean>"]], "isStatic": false}, {"name": "get_SuppressHandlerScope", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_SuppressHandlerScope", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.HttpMessageHandlerBuilder", "methods": [{"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Name", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_<PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Net.Http.HttpMessageHandler", "parameterTypes": [], "isStatic": false}, {"name": "set_<PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Void", "parameterTypes": [["value", "System.Net.Http.HttpMessageHandler"]], "isStatic": false}, {"name": "get_AdditionalHandlers", "returnType": "System.Collections.Generic.IList`1<System.Net.Http.DelegatingHandler>", "parameterTypes": [], "isStatic": false}, {"name": "get_Services", "returnType": "System.IServiceProvider", "parameterTypes": [], "isStatic": false}, {"name": "Build", "returnType": "System.Net.Http.HttpMessageHandler", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.IHttpMessageHandlerBuilderFilter", "methods": [{"name": "Configure", "returnType": "System.Action`1<Microsoft.Extensions.Http.HttpMessageHandlerBuilder>", "parameterTypes": [["next", "System.Action`1<Microsoft.Extensions.Http.HttpMessageHandlerBuilder>"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.ITypedHttpClientFactory`1", "methods": [{"name": "CreateClient", "returnType": "TClient", "parameterTypes": [["httpClient", "System.Net.Http.HttpClient"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.LifetimeTrackingHttpMessageHandler", "methods": [], "fields": []}, {"name": "Microsoft.Extensions.Http.LoggingHttpMessageHandlerBuilderFilter", "methods": [{"name": "Configure", "returnType": "System.Action`1<Microsoft.Extensions.Http.HttpMessageHandlerBuilder>", "parameterTypes": [["next", "System.Action`1<Microsoft.Extensions.Http.HttpMessageHandlerBuilder>"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.MetricsFactoryHttpMessageHandlerFilter", "methods": [{"name": "Configure", "returnType": "System.Action`1<Microsoft.Extensions.Http.HttpMessageHandlerBuilder>", "parameterTypes": [["next", "System.Action`1<Microsoft.Extensions.Http.HttpMessageHandlerBuilder>"]], "isStatic": false}], "fields": []}], "Microsoft.Extensions.Http.Logging": [{"name": "Microsoft.Extensions.Http.Logging.HttpClientLoggerHandler", "methods": [], "fields": []}, {"name": "Microsoft.Extensions.Http.Logging.HttpHeadersLogValue", "methods": [{"name": "get_Headers", "returnType": "System.Net.Http.Headers.HttpHeaders", "parameterTypes": [], "isStatic": false}, {"name": "get_ContentHeaders", "returnType": "System.Net.Http.Headers.HttpHeaders", "parameterTypes": [], "isStatic": false}, {"name": "get_Item", "returnType": "System.Collections.Generic.KeyValuePair`2<System.String,System.Object>", "parameterTypes": [["index", "System.Int32"]], "isStatic": false}, {"name": "get_Count", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "GetEnumerator", "returnType": "System.Collections.Generic.IEnumerator`1<System.Collections.Generic.KeyValuePair`2<System.String,System.Object>>", "parameterTypes": [], "isStatic": false}, {"name": "ToString", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.Logging.IHttpClientAsyncLogger", "methods": [{"name": "LogRequestStartAsync", "returnType": "System.Threading.Tasks.ValueTask`1<System.Object>", "parameterTypes": [["request", "System.Net.Http.HttpRequestMessage"], ["cancellationToken", "System.Threading.CancellationToken"]], "isStatic": false}, {"name": "LogRequestStopAsync", "returnType": "System.Threading.Tasks.ValueTask", "parameterTypes": [["context", "System.Object"], ["request", "System.Net.Http.HttpRequestMessage"], ["response", "System.Net.Http.HttpResponseMessage"], ["elapsed", "System.TimeSpan"], ["cancellationToken", "System.Threading.CancellationToken"]], "isStatic": false}, {"name": "LogRequestFailedAsync", "returnType": "System.Threading.Tasks.ValueTask", "parameterTypes": [["context", "System.Object"], ["request", "System.Net.Http.HttpRequestMessage"], ["response", "System.Net.Http.HttpResponseMessage"], ["exception", "System.Exception"], ["elapsed", "System.TimeSpan"], ["cancellationToken", "System.Threading.CancellationToken"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.Logging.IHttpClientLogger", "methods": [{"name": "LogRequestStart", "returnType": "System.Object", "parameterTypes": [["request", "System.Net.Http.HttpRequestMessage"]], "isStatic": false}, {"name": "LogRequestStop", "returnType": "System.Void", "parameterTypes": [["context", "System.Object"], ["request", "System.Net.Http.HttpRequestMessage"], ["response", "System.Net.Http.HttpResponseMessage"], ["elapsed", "System.TimeSpan"]], "isStatic": false}, {"name": "LogRequestFailed", "returnType": "System.Void", "parameterTypes": [["context", "System.Object"], ["request", "System.Net.Http.HttpRequestMessage"], ["response", "System.Net.Http.HttpResponseMessage"], ["exception", "System.Exception"], ["elapsed", "System.TimeSpan"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.Extensions.Http.Logging.LoggingHttpMessageHandler", "methods": [], "fields": []}, {"name": "Microsoft.Extensions.Http.Logging.LoggingScopeHttpMessageHandler", "methods": [], "fields": []}]}