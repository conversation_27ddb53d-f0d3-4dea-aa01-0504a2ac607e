{"Microsoft.Extensions.DependencyInjection": [{"name": "Microsoft.Extensions.DependencyInjection.IdentityServiceCollectionUIExtensions", "methods": [{"name": "AddDefaultIdentity", "returnType": "Microsoft.AspNetCore.Identity.IdentityBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"]], "isStatic": true}, {"name": "AddDefaultIdentity", "returnType": "Microsoft.AspNetCore.Identity.IdentityBuilder", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["configureOptions", "System.Action`1<Microsoft.AspNetCore.Identity.IdentityOptions>"]], "isStatic": true}], "fields": []}], "Microsoft.AspNetCore.Identity": [{"name": "Microsoft.AspNetCore.Identity.IdentityBuilderUIExtensions", "methods": [{"name": "AddDefaultUI", "returnType": "Microsoft.AspNetCore.Identity.IdentityBuilder", "parameterTypes": [["builder", "Microsoft.AspNetCore.Identity.IdentityBuilder"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.DefaultMessageEmailSender`1", "methods": [{"name": "SendConfirmationLinkAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["user", "TUser"], ["email", "System.String"], ["confirmationLink", "System.String"]], "isStatic": false}, {"name": "SendPasswordResetLinkAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["user", "TUser"], ["email", "System.String"], ["resetLink", "System.String"]], "isStatic": false}, {"name": "SendPasswordResetCodeAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["user", "TUser"], ["email", "System.String"], ["resetCode", "System.String"]], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI": [{"name": "Microsoft.AspNetCore.Identity.UI.IdentityDefaultUIAttribute", "methods": [{"name": "get_Template", "returnType": "System.Type", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.IdentityDefaultUIConfigureOptions`1", "methods": [{"name": "get_Environment", "returnType": "Microsoft.AspNetCore.Hosting.IWebHostEnvironment", "parameterTypes": [], "isStatic": false}, {"name": "PostConfigure", "returnType": "System.Void", "parameterTypes": [["name", "System.String"], ["options", "Microsoft.AspNetCore.Mvc.RazorPages.RazorPagesOptions"]], "isStatic": false}, {"name": "Configure", "returnType": "System.Void", "parameterTypes": [["options", "Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationOptions"]], "isStatic": false}, {"name": "Configure", "returnType": "System.Void", "parameterTypes": [["name", "System.String"], ["options", "Microsoft.AspNetCore.Authentication.Cookies.CookieAuthenticationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.IdentityPageModelConvention`1", "methods": [{"name": "Apply", "returnType": "System.Void", "parameterTypes": [["model", "Microsoft.AspNetCore.Mvc.ApplicationModels.PageApplicationModel"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.LoggerEventIds", "methods": [], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.UIFramework", "methods": [], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.UIFrameworkAttribute", "methods": [{"name": "get_UIFramework", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal": [{"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.ErrorModel", "methods": [{"name": "get_RequestId", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_RequestId", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ShowRequestId", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Areas_Identity_Pages_V5_Error", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.ErrorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.ErrorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.ErrorModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Areas_Identity_Pages_V5__Layout", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_Engine", "returnType": "Microsoft.AspNetCore.Mvc.ViewEngines.ICompositeViewEngine", "parameterTypes": [], "isStatic": false}, {"name": "get_Environment", "returnType": "Microsoft.AspNetCore.Hosting.IWebHostEnvironment", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Areas_Identity_Pages_V5__ValidationScriptsPartial", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Areas_Identity_Pages_V5__ViewImports", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Areas_Identity_Pages_V5__ViewStart", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account": [{"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_AccessDenied", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.AccessDeniedModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.AccessDeniedModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.AccessDeniedModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_ConfirmEmail", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ConfirmEmailModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ConfirmEmailModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ConfirmEmailModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_ConfirmEmailChange", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ConfirmEmailChangeModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ConfirmEmailChangeModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ConfirmEmailChangeModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_ExternalLogin", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ExternalLoginModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ExternalLoginModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ExternalLoginModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_ForgotPassword", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_ForgotPasswordConfirmation", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordConfirmation>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordConfirmation>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordConfirmation", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_Lockout", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LockoutModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LockoutModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LockoutModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_Login", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_LoginWith2fa", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWith2faModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWith2faModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWith2faModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_LoginWithRecoveryCode", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWithRecoveryCodeModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWithRecoveryCodeModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWithRecoveryCodeModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_Logout", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LogoutModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LogoutModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LogoutModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_Register", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_RegisterConfirmation", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterConfirmationModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_ResendEmailConfirmation", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResendEmailConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResendEmailConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResendEmailConfirmationModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_ResetPassword", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account_ResetPasswordConfirmation", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordConfirmationModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account__StatusMessage", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.String>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Areas_Identity_Pages_V5_Account__ViewImports", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage": [{"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_ChangePassword", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ChangePasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ChangePasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ChangePasswordModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_DeletePersonalData", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DeletePersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DeletePersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DeletePersonalDataModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_Disable2fa", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.Disable2faModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.Disable2faModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.Disable2faModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_DownloadPersonalData", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DownloadPersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DownloadPersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DownloadPersonalDataModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_Email", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EmailModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EmailModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EmailModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_EnableAuthenticator", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EnableAuthenticatorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EnableAuthenticatorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EnableAuthenticatorModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_ExternalLogins", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ExternalLoginsModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ExternalLoginsModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ExternalLoginsModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_GenerateRecoveryCodes", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.GenerateRecoveryCodesModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.GenerateRecoveryCodesModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.GenerateRecoveryCodesModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_Index", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.IndexModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.IndexModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.IndexModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_PersonalData", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.PersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.PersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.PersonalDataModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_ResetAuthenticator", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ResetAuthenticatorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ResetAuthenticatorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ResetAuthenticatorModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_SetPassword", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.SetPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.SetPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.SetPasswordModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_ShowRecoveryCodes", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ShowRecoveryCodesModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ShowRecoveryCodesModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ShowRecoveryCodesModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage_TwoFactorAuthentication", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.TwoFactorAuthenticationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.TwoFactorAuthenticationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.TwoFactorAuthenticationModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage__Layout", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage__ManageNav", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage__StatusMessage", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.String>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage__ViewImports", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Internal.Account.Manage.Areas_Identity_Pages_V5_Account_Manage__ViewStart", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal": [{"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.ChangePasswordModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.ChangePasswordModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.ChangePasswordModel/InputModel"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.ChangePasswordModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.DeletePersonalDataModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.DeletePersonalDataModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.DeletePersonalDataModel/InputModel"]], "isStatic": false}, {"name": "get_RequirePassword", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_RequirePassword", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.DeletePersonalDataModel`1", "methods": [{"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.Disable2faModel", "methods": [{"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.Disable2faModel`1", "methods": [{"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.DownloadPersonalDataModel", "methods": [{"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.DownloadPersonalDataModel`1", "methods": [{"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.EmailModel", "methods": [{"name": "get_Email", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Email", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_IsEmailConfirmed", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_IsEmailConfirmed", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.EmailModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.EmailModel/InputModel"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostChangeEmailAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostSendVerificationEmailAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.EmailModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostChangeEmailAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostSendVerificationEmailAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.EnableAuthenticatorModel", "methods": [{"name": "get_SharedKey", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_SharedKey", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_AuthenticatorUri", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_AuthenticatorUri", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_RecoveryCodes", "returnType": "System.String[]", "parameterTypes": [], "isStatic": false}, {"name": "set_RecoveryCodes", "returnType": "System.Void", "parameterTypes": [["value", "System.String[]"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.EnableAuthenticatorModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.EnableAuthenticatorModel/InputModel"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.EnableAuthenticatorModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.ExternalLoginsModel", "methods": [{"name": "get_Current<PERSON><PERSON>ins", "returnType": "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Identity.UserLoginInfo>", "parameterTypes": [], "isStatic": false}, {"name": "set_CurrentLogins", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Identity.UserLoginInfo>"]], "isStatic": false}, {"name": "get_Other<PERSON><PERSON>ins", "returnType": "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>", "parameterTypes": [], "isStatic": false}, {"name": "set_OtherLogins", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>"]], "isStatic": false}, {"name": "get_ShowRemoveButton", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_ShowRemoveButton", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostRemoveLoginAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["loginProvider", "System.String"], ["providerKey", "System.String"]], "isStatic": false}, {"name": "OnPostLinkLoginAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["provider", "System.String"]], "isStatic": false}, {"name": "OnGetLinkLoginCallbackAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.ExternalLoginsModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostRemoveLoginAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["loginProvider", "System.String"], ["providerKey", "System.String"]], "isStatic": false}, {"name": "OnPostLinkLoginAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["provider", "System.String"]], "isStatic": false}, {"name": "OnGetLinkLoginCallbackAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.GenerateRecoveryCodesModel", "methods": [{"name": "get_RecoveryCodes", "returnType": "System.String[]", "parameterTypes": [], "isStatic": false}, {"name": "set_RecoveryCodes", "returnType": "System.Void", "parameterTypes": [["value", "System.String[]"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.GenerateRecoveryCodesModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.IndexModel", "methods": [{"name": "get_Username", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Username", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.IndexModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.IndexModel/InputModel"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.IndexModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.ManageNavPages", "methods": [{"name": "get_Index", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_Email", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_ChangePassword", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_DownloadPersonalData", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_DeletePersonalData", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_External<PERSON><PERSON>ins", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_PersonalData", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_TwoFactorAuthentication", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "IndexNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "EmailNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "ChangePasswordNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "DownloadPersonalDataNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "DeletePersonalDataNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "ExternalLoginsNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "PersonalDataNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "TwoFactorAuthenticationNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "PageNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"], ["page", "System.String"]], "isStatic": true}, {"name": "IndexAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "EmailAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "ChangePasswordAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "DownloadPersonalDataAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "DeletePersonalDataAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "ExternalLoginsAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "PersonalDataAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "TwoFactorAuthenticationAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "AriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"], ["page", "System.String"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.PersonalDataModel", "methods": [{"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.PersonalDataModel`1", "methods": [{"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.ResetAuthenticatorModel", "methods": [{"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.ResetAuthenticatorModel`1", "methods": [{"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.SetPasswordModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.SetPasswordModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.SetPasswordModel/InputModel"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.SetPasswordModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.ShowRecoveryCodesModel", "methods": [{"name": "get_RecoveryCodes", "returnType": "System.String[]", "parameterTypes": [], "isStatic": false}, {"name": "set_RecoveryCodes", "returnType": "System.Void", "parameterTypes": [["value", "System.String[]"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.TwoFactorAuthenticationModel", "methods": [{"name": "get_HasAuthenticator", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_HasAuthenticator", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_RecoveryCodesLeft", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "set_RecoveryCodesLeft", "returnType": "System.Void", "parameterTypes": [["value", "System.Int32"]], "isStatic": false}, {"name": "get_Is2faEnabled", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_Is2faEnabled", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_IsMachineRemembered", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_IsMachineRemembered", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Manage.Internal.TwoFactorAuthenticationModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal": [{"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.AccessDeniedModel", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ConfirmEmailModel", "methods": [{"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["userId", "System.String"], ["code", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ConfirmEmailModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["userId", "System.String"], ["code", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ConfirmEmailChangeModel", "methods": [{"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["userId", "System.String"], ["email", "System.String"], ["code", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ConfirmEmailChangeModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["userId", "System.String"], ["email", "System.String"], ["code", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ExternalLoginModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ExternalLoginModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ExternalLoginModel/InputModel"]], "isStatic": false}, {"name": "get_ProviderDisplayName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ProviderDisplayName", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ReturnUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ReturnUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ErrorMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ErrorMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [], "isStatic": false}, {"name": "OnPost", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [["provider", "System.String"], ["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnGetCallbackAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"], ["remoteError", "System.String"]], "isStatic": false}, {"name": "OnPostConfirmationAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ExternalLoginModel`1", "methods": [{"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [], "isStatic": false}, {"name": "OnPost", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [["provider", "System.String"], ["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnGetCallbackAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"], ["remoteError", "System.String"]], "isStatic": false}, {"name": "OnPostConfirmationAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordModel/InputModel"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordModel`1", "methods": [{"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ForgotPasswordConfirmation", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LockoutModel", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel/InputModel"]], "isStatic": false}, {"name": "get_External<PERSON><PERSON>ins", "returnType": "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>", "parameterTypes": [], "isStatic": false}, {"name": "set_ExternalLogins", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>"]], "isStatic": false}, {"name": "get_ReturnUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ReturnUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ErrorMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ErrorMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWith2faModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWith2faModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWith2faModel/InputModel"]], "isStatic": false}, {"name": "get_RememberMe", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_RememberMe", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_ReturnUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ReturnUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["rememberMe", "System.Boolean"], ["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["rememberMe", "System.Boolean"], ["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWith2faModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["rememberMe", "System.Boolean"], ["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["rememberMe", "System.Boolean"], ["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWithRecoveryCodeModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWithRecoveryCodeModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWithRecoveryCodeModel/InputModel"]], "isStatic": false}, {"name": "get_ReturnUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ReturnUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LoginWithRecoveryCodeModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LogoutModel", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "OnPost", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.LogoutModel`1", "methods": [{"name": "OnPost", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterModel/InputModel"]], "isStatic": false}, {"name": "get_ReturnUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ReturnUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_External<PERSON><PERSON>ins", "returnType": "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>", "parameterTypes": [], "isStatic": false}, {"name": "set_ExternalLogins", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterConfirmationModel", "methods": [{"name": "get_Email", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Email", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_DisplayConfirmAccountLink", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_DisplayConfirmAccountLink", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_EmailConfirmationUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_EmailConfirmationUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["email", "System.String"], ["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.RegisterConfirmationModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["email", "System.String"], ["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResendEmailConfirmationModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResendEmailConfirmationModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResendEmailConfirmationModel/InputModel"]], "isStatic": false}, {"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResendEmailConfirmationModel`1", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordModel/InputModel"]], "isStatic": false}, {"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [["code", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordModel`1", "methods": [{"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [["code", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V5.Pages.Account.Internal.ResetPasswordConfirmationModel", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal": [{"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.ErrorModel", "methods": [{"name": "get_RequestId", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_RequestId", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ShowRequestId", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Areas_Identity_Pages_V4_Error", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.ErrorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.ErrorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.ErrorModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Areas_Identity_Pages_V4__Layout", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_Engine", "returnType": "Microsoft.AspNetCore.Mvc.ViewEngines.ICompositeViewEngine", "parameterTypes": [], "isStatic": false}, {"name": "get_Environment", "returnType": "Microsoft.AspNetCore.Hosting.IWebHostEnvironment", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Areas_Identity_Pages_V4__ValidationScriptsPartial", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Areas_Identity_Pages_V4__ViewImports", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Areas_Identity_Pages_V4__ViewStart", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account": [{"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_AccessDenied", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.AccessDeniedModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.AccessDeniedModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.AccessDeniedModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_ConfirmEmail", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ConfirmEmailModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ConfirmEmailModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ConfirmEmailModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_ConfirmEmailChange", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ConfirmEmailChangeModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ConfirmEmailChangeModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ConfirmEmailChangeModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_ExternalLogin", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ExternalLoginModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ExternalLoginModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ExternalLoginModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_ForgotPassword", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_ForgotPasswordConfirmation", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordConfirmation>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordConfirmation>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordConfirmation", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_Lockout", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LockoutModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LockoutModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LockoutModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_Login", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_LoginWith2fa", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWith2faModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWith2faModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWith2faModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_LoginWithRecoveryCode", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWithRecoveryCodeModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWithRecoveryCodeModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWithRecoveryCodeModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_Logout", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LogoutModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LogoutModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LogoutModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_Register", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_RegisterConfirmation", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterConfirmationModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_ResendEmailConfirmation", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResendEmailConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResendEmailConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResendEmailConfirmationModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_ResetPassword", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account_ResetPasswordConfirmation", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordConfirmationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordConfirmationModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account__StatusMessage", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.String>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Areas_Identity_Pages_V4_Account__ViewImports", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage": [{"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_ChangePassword", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ChangePasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ChangePasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ChangePasswordModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_DeletePersonalData", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DeletePersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DeletePersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DeletePersonalDataModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_Disable2fa", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.Disable2faModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.Disable2faModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.Disable2faModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_DownloadPersonalData", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DownloadPersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DownloadPersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DownloadPersonalDataModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_Email", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EmailModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EmailModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EmailModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_EnableAuthenticator", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EnableAuthenticatorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EnableAuthenticatorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EnableAuthenticatorModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_ExternalLogins", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ExternalLoginsModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ExternalLoginsModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ExternalLoginsModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_GenerateRecoveryCodes", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.GenerateRecoveryCodesModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.GenerateRecoveryCodesModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.GenerateRecoveryCodesModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_Index", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.IndexModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.IndexModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.IndexModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_PersonalData", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.PersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.PersonalDataModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.PersonalDataModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_ResetAuthenticator", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ResetAuthenticatorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ResetAuthenticatorModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ResetAuthenticatorModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_SetPassword", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.SetPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.SetPasswordModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.SetPasswordModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_ShowRecoveryCodes", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ShowRecoveryCodesModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ShowRecoveryCodesModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ShowRecoveryCodesModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage_TwoFactorAuthentication", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.TwoFactorAuthenticationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_ViewData", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.ViewDataDictionary`1<Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.TwoFactorAuthenticationModel>", "parameterTypes": [], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.TwoFactorAuthenticationModel", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage__Layout", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage__ManageNav", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage__StatusMessage", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.String>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage__ViewImports", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Internal.Account.Manage.Areas_Identity_Pages_V4_Account_Manage__ViewStart", "methods": [{"name": "ExecuteAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [], "isStatic": false}, {"name": "get_ModelExpressionProvider", "returnType": "Microsoft.AspNetCore.Mvc.ViewFeatures.IModelExpressionProvider", "parameterTypes": [], "isStatic": false}, {"name": "get_Url", "returnType": "Microsoft.AspNetCore.Mvc.IUrlHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Component", "returnType": "Microsoft.AspNetCore.Mvc.IViewComponentHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON>", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IJsonHelper", "parameterTypes": [], "isStatic": false}, {"name": "get_Html", "returnType": "Microsoft.AspNetCore.Mvc.Rendering.IHtmlHelper`1<System.Object>", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal": [{"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ChangePasswordModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ChangePasswordModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ChangePasswordModel/InputModel"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ChangePasswordModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DeletePersonalDataModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DeletePersonalDataModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DeletePersonalDataModel/InputModel"]], "isStatic": false}, {"name": "get_RequirePassword", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_RequirePassword", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DeletePersonalDataModel`1", "methods": [{"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.Disable2faModel", "methods": [{"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.Disable2faModel`1", "methods": [{"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DownloadPersonalDataModel", "methods": [{"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.DownloadPersonalDataModel`1", "methods": [{"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EmailModel", "methods": [{"name": "get_Email", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Email", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_IsEmailConfirmed", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_IsEmailConfirmed", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EmailModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EmailModel/InputModel"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostChangeEmailAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostSendVerificationEmailAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EmailModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostChangeEmailAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostSendVerificationEmailAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EnableAuthenticatorModel", "methods": [{"name": "get_SharedKey", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_SharedKey", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_AuthenticatorUri", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_AuthenticatorUri", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_RecoveryCodes", "returnType": "System.String[]", "parameterTypes": [], "isStatic": false}, {"name": "set_RecoveryCodes", "returnType": "System.Void", "parameterTypes": [["value", "System.String[]"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EnableAuthenticatorModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EnableAuthenticatorModel/InputModel"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.EnableAuthenticatorModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ExternalLoginsModel", "methods": [{"name": "get_Current<PERSON><PERSON>ins", "returnType": "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Identity.UserLoginInfo>", "parameterTypes": [], "isStatic": false}, {"name": "set_CurrentLogins", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Identity.UserLoginInfo>"]], "isStatic": false}, {"name": "get_Other<PERSON><PERSON>ins", "returnType": "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>", "parameterTypes": [], "isStatic": false}, {"name": "set_OtherLogins", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>"]], "isStatic": false}, {"name": "get_ShowRemoveButton", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_ShowRemoveButton", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostRemoveLoginAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["loginProvider", "System.String"], ["providerKey", "System.String"]], "isStatic": false}, {"name": "OnPostLinkLoginAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["provider", "System.String"]], "isStatic": false}, {"name": "OnGetLinkLoginCallbackAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ExternalLoginsModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostRemoveLoginAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["loginProvider", "System.String"], ["providerKey", "System.String"]], "isStatic": false}, {"name": "OnPostLinkLoginAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["provider", "System.String"]], "isStatic": false}, {"name": "OnGetLinkLoginCallbackAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.GenerateRecoveryCodesModel", "methods": [{"name": "get_RecoveryCodes", "returnType": "System.String[]", "parameterTypes": [], "isStatic": false}, {"name": "set_RecoveryCodes", "returnType": "System.Void", "parameterTypes": [["value", "System.String[]"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.GenerateRecoveryCodesModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.IndexModel", "methods": [{"name": "get_Username", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Username", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.IndexModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.IndexModel/InputModel"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.IndexModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ManageNavPages", "methods": [{"name": "get_Index", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_Email", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_ChangePassword", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_DownloadPersonalData", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_DeletePersonalData", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_External<PERSON><PERSON>ins", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_PersonalData", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_TwoFactorAuthentication", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "IndexNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "EmailNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "ChangePasswordNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "DownloadPersonalDataNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "DeletePersonalDataNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "ExternalLoginsNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "PersonalDataNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "TwoFactorAuthenticationNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "PageNavClass", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"], ["page", "System.String"]], "isStatic": true}, {"name": "IndexAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "EmailAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "ChangePasswordAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "DownloadPersonalDataAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "DeletePersonalDataAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "ExternalLoginsAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "PersonalDataAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "TwoFactorAuthenticationAriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"]], "isStatic": true}, {"name": "AriaCurrent", "returnType": "System.String", "parameterTypes": [["viewContext", "Microsoft.AspNetCore.Mvc.Rendering.ViewContext"], ["page", "System.String"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.PersonalDataModel", "methods": [{"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.PersonalDataModel`1", "methods": [{"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ResetAuthenticatorModel", "methods": [{"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ResetAuthenticatorModel`1", "methods": [{"name": "OnGet", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.SetPasswordModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.SetPasswordModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.SetPasswordModel/InputModel"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.SetPasswordModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.ShowRecoveryCodesModel", "methods": [{"name": "get_RecoveryCodes", "returnType": "System.String[]", "parameterTypes": [], "isStatic": false}, {"name": "set_RecoveryCodes", "returnType": "System.Void", "parameterTypes": [["value", "System.String[]"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.TwoFactorAuthenticationModel", "methods": [{"name": "get_HasAuthenticator", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_HasAuthenticator", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_RecoveryCodesLeft", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "set_RecoveryCodesLeft", "returnType": "System.Void", "parameterTypes": [["value", "System.Int32"]], "isStatic": false}, {"name": "get_Is2faEnabled", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_Is2faEnabled", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_IsMachineRemembered", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_IsMachineRemembered", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Manage.Internal.TwoFactorAuthenticationModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal": [{"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.AccessDeniedModel", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ConfirmEmailModel", "methods": [{"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["userId", "System.String"], ["code", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ConfirmEmailModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["userId", "System.String"], ["code", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ConfirmEmailChangeModel", "methods": [{"name": "get_StatusMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_StatusMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["userId", "System.String"], ["email", "System.String"], ["code", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ConfirmEmailChangeModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["userId", "System.String"], ["email", "System.String"], ["code", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ExternalLoginModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ExternalLoginModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ExternalLoginModel/InputModel"]], "isStatic": false}, {"name": "get_ProviderDisplayName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ProviderDisplayName", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ReturnUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ReturnUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ErrorMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ErrorMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [], "isStatic": false}, {"name": "OnPost", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [["provider", "System.String"], ["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnGetCallbackAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"], ["remoteError", "System.String"]], "isStatic": false}, {"name": "OnPostConfirmationAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ExternalLoginModel`1", "methods": [{"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [], "isStatic": false}, {"name": "OnPost", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [["provider", "System.String"], ["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnGetCallbackAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"], ["remoteError", "System.String"]], "isStatic": false}, {"name": "OnPostConfirmationAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordModel/InputModel"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordModel`1", "methods": [{"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ForgotPasswordConfirmation", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LockoutModel", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginModel/InputModel"]], "isStatic": false}, {"name": "get_External<PERSON><PERSON>ins", "returnType": "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>", "parameterTypes": [], "isStatic": false}, {"name": "set_ExternalLogins", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>"]], "isStatic": false}, {"name": "get_ReturnUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ReturnUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ErrorMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ErrorMessage", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWith2faModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWith2faModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWith2faModel/InputModel"]], "isStatic": false}, {"name": "get_RememberMe", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_RememberMe", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_ReturnUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ReturnUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["rememberMe", "System.Boolean"], ["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["rememberMe", "System.Boolean"], ["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWith2faModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["rememberMe", "System.Boolean"], ["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["rememberMe", "System.Boolean"], ["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWithRecoveryCodeModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWithRecoveryCodeModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWithRecoveryCodeModel/InputModel"]], "isStatic": false}, {"name": "get_ReturnUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ReturnUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LoginWithRecoveryCodeModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LogoutModel", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "OnPost", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.LogoutModel`1", "methods": [{"name": "OnPost", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterModel/InputModel"]], "isStatic": false}, {"name": "get_ReturnUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ReturnUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_External<PERSON><PERSON>ins", "returnType": "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>", "parameterTypes": [], "isStatic": false}, {"name": "set_ExternalLogins", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IList`1<Microsoft.AspNetCore.Authentication.AuthenticationScheme>"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterConfirmationModel", "methods": [{"name": "get_Email", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Email", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_DisplayConfirmAccountLink", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_DisplayConfirmAccountLink", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_EmailConfirmationUrl", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_EmailConfirmationUrl", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["email", "System.String"], ["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.RegisterConfirmationModel`1", "methods": [{"name": "OnGetAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [["email", "System.String"], ["returnUrl", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResendEmailConfirmationModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResendEmailConfirmationModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResendEmailConfirmationModel/InputModel"]], "isStatic": false}, {"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResendEmailConfirmationModel`1", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordModel", "methods": [{"name": "get_Input", "returnType": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordModel/InputModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Input", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordModel/InputModel"]], "isStatic": false}, {"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [["code", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordModel`1", "methods": [{"name": "OnGet", "returnType": "Microsoft.AspNetCore.Mvc.IActionResult", "parameterTypes": [["code", "System.String"]], "isStatic": false}, {"name": "OnPostAsync", "returnType": "System.Threading.Tasks.Task`1<Microsoft.AspNetCore.Mvc.IActionResult>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.AspNetCore.Identity.UI.V4.Pages.Account.Internal.ResetPasswordConfirmationModel", "methods": [{"name": "OnGet", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.AspNetCore.Identity.UI.Areas.Identity.Filters": [{"name": "Microsoft.AspNetCore.Identity.UI.Areas.Identity.Filters.ExternalLoginsPageFilter`1", "methods": [{"name": "OnPageHandlerExecutionAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["context", "Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutingContext"], ["next", "Microsoft.AspNetCore.Mvc.Filters.PageHandlerExecutionDelegate"]], "isStatic": false}, {"name": "OnPageHandlerSelectionAsync", "returnType": "System.Threading.Tasks.Task", "parameterTypes": [["context", "Microsoft.AspNetCore.Mvc.Filters.PageHandlerSelectedContext"]], "isStatic": false}], "fields": []}]}