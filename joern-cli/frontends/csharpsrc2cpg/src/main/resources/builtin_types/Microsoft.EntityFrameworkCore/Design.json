{"JetBrains.Annotations": [{"name": "JetBrains.Annotations.InvokerParameterNameAttribute", "methods": [], "fields": []}, {"name": "JetBrains.Annotations.NoEnumerationAttribute", "methods": [], "fields": []}, {"name": "JetBrains.Annotations.ContractAnnotationAttribute", "methods": [{"name": "get_Contract", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_ForceFullStates", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "JetBrains.Annotations.UsedImplicitlyAttribute", "methods": [{"name": "get_UseKindFlags", "returnType": "JetBrains.Annotations.ImplicitUseKindFlags", "parameterTypes": [], "isStatic": false}, {"name": "get_TargetFlags", "returnType": "JetBrains.Annotations.ImplicitUseTargetFlags", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "JetBrains.Annotations.StringFormatMethodAttribute", "methods": [{"name": "get_FormatParameterName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "JetBrains.Annotations.ImplicitUseKindFlags", "methods": [], "fields": []}, {"name": "JetBrains.Annotations.ImplicitUseTargetFlags", "methods": [], "fields": []}], "System": [{"name": "System.SharedTypeExtensions", "methods": [{"name": "UnwrapNullableType", "returnType": "System.Type", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "IsNullableValueType", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "IsNullableType", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "IsValidEntityType", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "IsValidComplexType", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "IsScalarType", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "IsPropertyBagType", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "MakeNullable", "returnType": "System.Type", "parameterTypes": [["type", "System.Type"], ["nullable", "System.Boolean"]], "isStatic": true}, {"name": "IsNumeric", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "IsInteger", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "IsSignedInteger", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "IsAnonymousType", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetAnyProperty", "returnType": "System.Reflection.PropertyInfo", "parameterTypes": [["type", "System.Type"], ["name", "System.String"]], "isStatic": true}, {"name": "IsInstantiable", "returnType": "System.Boolean", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "UnwrapEnumType", "returnType": "System.Type", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetSequenceType", "returnType": "System.Type", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "TryGetSequenceType", "returnType": "System.Type", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "TryGetElementType", "returnType": "System.Type", "parameterTypes": [["type", "System.Type"], ["interfaceOrBaseType", "System.Type"]], "isStatic": true}, {"name": "IsCompatibleWith", "returnType": "System.Boolean", "parameterTypes": [["propertyType", "System.Type"], ["fieldType", "System.Type"]], "isStatic": true}, {"name": "GetGenericTypeImplementations", "returnType": "System.Collections.Generic.IEnumerable`1<System.Type>", "parameterTypes": [["type", "System.Type"], ["interfaceOrBaseType", "System.Type"]], "isStatic": true}, {"name": "GetBaseTypes", "returnType": "System.Collections.Generic.IEnumerable`1<System.Type>", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetBaseTypesAndInterfacesInclusive", "returnType": "System.Collections.Generic.List`1<System.Type>", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetTypesInHierarchy", "returnType": "System.Collections.Generic.IEnumerable`1<System.Type>", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetDeclaredInterfaces", "returnType": "System.Collections.Generic.IEnumerable`1<System.Type>", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetDeclaredConstructor", "returnType": "System.Reflection.ConstructorInfo", "parameterTypes": [["type", "System.Type"], ["types", "System.Type[]"]], "isStatic": true}, {"name": "GetPropertiesInHierarchy", "returnType": "System.Collections.Generic.IEnumerable`1<System.Reflection.PropertyInfo>", "parameterTypes": [["type", "System.Type"], ["name", "System.String"]], "isStatic": true}, {"name": "GetMembersInHierarchy", "returnType": "System.Collections.Generic.IEnumerable`1<System.Reflection.MemberInfo>", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetMembersInHierarchy", "returnType": "System.Collections.Generic.IEnumerable`1<System.Reflection.MemberInfo>", "parameterTypes": [["type", "System.Type"], ["name", "System.String"]], "isStatic": true}, {"name": "GetDefaultValue", "returnType": "System.Object", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetConstructibleTypes", "returnType": "System.Collections.Generic.IEnumerable`1<System.Reflection.TypeInfo>", "parameterTypes": [["assembly", "System.Reflection.Assembly"]], "isStatic": true}, {"name": "GetLoadableDefinedTypes", "returnType": "System.Collections.Generic.IEnumerable`1<System.Reflection.TypeInfo>", "parameterTypes": [["assembly", "System.Reflection.Assembly"]], "isStatic": true}, {"name": "DisplayName", "returnType": "System.String", "parameterTypes": [["type", "System.Type"], ["fullName", "System.Boolean"], ["compilable", "System.Boolean"]], "isStatic": true}, {"name": "GetNamespaces", "returnType": "System.Collections.Generic.IEnumerable`1<System.String>", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetDefaultValueConstant", "returnType": "System.Linq.Expressions.ConstantExpression", "parameterTypes": [["type", "System.Type"]], "isStatic": true}], "fields": []}], "System.Text": [{"name": "System.Text.StringBuilderExtensions", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Text.StringBuilder", "parameterTypes": [["stringBuilder", "System.Text.StringBuilder"], ["values", "System.Collections.Generic.IEnumerable`1<System.String>"], ["separator", "System.String"]], "isStatic": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Text.StringBuilder", "parameterTypes": [["stringBuilder", "System.Text.StringBuilder"], ["separator", "System.String"], ["values", "System.String[]"]], "isStatic": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Text.StringBuilder", "parameterTypes": [["stringBuilder", "System.Text.StringBuilder"], ["values", "System.Collections.Generic.IEnumerable`1<T>"], ["joinAction", "System.Action`2<System.Text.StringBuilder,T>"], ["separator", "System.String"]], "isStatic": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Text.StringBuilder", "parameterTypes": [["stringBuilder", "System.Text.StringBuilder"], ["values", "System.Collections.Generic.IEnumerable`1<T>"], ["joinFunc", "System.Func`3<System.Text.StringBuilder,T,System.Boolean>"], ["separator", "System.String"]], "isStatic": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Text.StringBuilder", "parameterTypes": [["stringBuilder", "System.Text.StringBuilder"], ["values", "System.Collections.Generic.IEnumerable`1<T>"], ["param", "TParam"], ["joinAction", "System.Action`3<System.Text.StringBuilder,T,TParam>"], ["separator", "System.String"]], "isStatic": true}, {"name": "AppendBytes", "returnType": "System.Void", "parameterTypes": [["builder", "System.Text.StringBuilder"], ["bytes", "System.Byte[]"]], "isStatic": true}], "fields": []}], "System.Reflection": [{"name": "System.Reflection.EntityFrameworkMemberInfoExtensions", "methods": [{"name": "GetMemberType", "returnType": "System.Type", "parameterTypes": [["memberInfo", "System.Reflection.MemberInfo"]], "isStatic": true}, {"name": "IsSameAs", "returnType": "System.Boolean", "parameterTypes": [["propertyInfo", "System.Reflection.MemberInfo"], ["otherPropertyInfo", "System.Reflection.MemberInfo"]], "isStatic": true}, {"name": "IsOverriddenBy", "returnType": "System.Boolean", "parameterTypes": [["propertyInfo", "System.Reflection.MemberInfo"], ["otherPropertyInfo", "System.Reflection.MemberInfo"]], "isStatic": true}, {"name": "GetSimpleMemberName", "returnType": "System.String", "parameterTypes": [["member", "System.Reflection.MemberInfo"]], "isStatic": true}, {"name": "IsReallyVirtual", "returnType": "System.Boolean", "parameterTypes": [["method", "System.Reflection.MethodInfo"]], "isStatic": true}], "fields": []}, {"name": "System.Reflection.MethodInfoExtensions", "methods": [{"name": "IsContainsMethod", "returnType": "System.Boolean", "parameterTypes": [["method", "System.Reflection.MethodInfo"]], "isStatic": true}], "fields": []}, {"name": "System.Reflection.PropertyInfoExtensions", "methods": [{"name": "IsStatic", "returnType": "System.Boolean", "parameterTypes": [["property", "System.Reflection.PropertyInfo"]], "isStatic": true}, {"name": "IsCandidateProperty", "returnType": "System.Boolean", "parameterTypes": [["memberInfo", "System.Reflection.MemberInfo"], ["needsWrite", "System.Boolean"], ["publicOnly", "System.Boolean"]], "isStatic": true}, {"name": "IsIndexerProperty", "returnType": "System.Boolean", "parameterTypes": [["propertyInfo", "System.Reflection.PropertyInfo"]], "isStatic": true}, {"name": "FindGetterProperty", "returnType": "System.Reflection.PropertyInfo", "parameterTypes": [["propertyInfo", "System.Reflection.PropertyInfo"]], "isStatic": true}, {"name": "FindSetterProperty", "returnType": "System.Reflection.PropertyInfo", "parameterTypes": [["propertyInfo", "System.Reflection.PropertyInfo"]], "isStatic": true}], "fields": []}], "System.Linq.Expressions": [{"name": "System.Linq.Expressions.ExpressionExtensions", "methods": [{"name": "IsNullConstantExpression", "returnType": "System.Boolean", "parameterTypes": [["expression", "System.Linq.Expressions.Expression"]], "isStatic": true}, {"name": "UnwrapLambdaFromQuote", "returnType": "System.Linq.Expressions.LambdaExpression", "parameterTypes": [["expression", "System.Linq.Expressions.Expression"]], "isStatic": true}, {"name": "UnwrapTypeConversion", "returnType": "System.Linq.Expressions.Expression", "parameterTypes": [["expression", "System.Linq.Expressions.Expression"], ["convertedType", "System.Type&"]], "isStatic": true}, {"name": "GetConstantValue", "returnType": "T", "parameterTypes": [["expression", "System.Linq.Expressions.Expression"]], "isStatic": true}], "fields": []}, {"name": "System.Linq.Expressions.ExpressionVisitorExtensions", "methods": [{"name": "Visit", "returnType": "System.Collections.Generic.IReadOnlyList`1<System.Linq.Expressions.Expression>", "parameterTypes": [["visitor", "System.Linq.Expressions.ExpressionVisitor"], ["nodes", "System.Collections.Generic.IReadOnlyList`1<System.Linq.Expressions.Expression>"]], "isStatic": true}, {"name": "VisitAndConvert", "returnType": "System.Collections.Generic.IReadOnlyList`1<T>", "parameterTypes": [["visitor", "System.Linq.Expressions.ExpressionVisitor"], ["nodes", "System.Collections.Generic.IReadOnlyList`1<T>"], ["callerName", "System.String"]], "isStatic": true}, {"name": "Visit", "returnType": "System.Collections.Generic.IReadOnlyList`1<T>", "parameterTypes": [["visitor", "System.Linq.Expressions.ExpressionVisitor"], ["nodes", "System.Collections.Generic.IReadOnlyList`1<T>"], ["elementVisitor", "System.Func`2<T,T>"]], "isStatic": true}], "fields": []}], "System.Runtime.Remoting.Messaging": [{"name": "System.Runtime.Remoting.Messaging.CallContext", "methods": [{"name": "LogicalGetData", "returnType": "System.Object", "parameterTypes": [["name", "System.String"]], "isStatic": true}], "fields": []}], "Microsoft.DotNet.Cli.CommandLine": [{"name": "Microsoft.DotNet.Cli.CommandLine.CommandArgument", "methods": [{"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Name", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Description", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Description", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Values", "returnType": "System.Collections.Generic.List`1<System.String>", "parameterTypes": [], "isStatic": false}, {"name": "get_MultipleValues", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_MultipleValues", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_Value", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.DotNet.Cli.CommandLine.CommandLineApplication", "methods": [{"name": "get_Parent", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandLineApplication", "parameterTypes": [], "isStatic": false}, {"name": "set_Parent", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.DotNet.Cli.CommandLine.CommandLineApplication"]], "isStatic": false}, {"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Name", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_FullName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_FullName", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Syntax", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Syntax", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Description", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Description", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Options", "returnType": "System.Collections.Generic.List`1<Microsoft.DotNet.Cli.CommandLine.CommandOption>", "parameterTypes": [], "isStatic": false}, {"name": "get_OptionHelp", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandOption", "parameterTypes": [], "isStatic": false}, {"name": "get_OptionVersion", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandOption", "parameterTypes": [], "isStatic": false}, {"name": "get_Arguments", "returnType": "System.Collections.Generic.List`1<Microsoft.DotNet.Cli.CommandLine.CommandArgument>", "parameterTypes": [], "isStatic": false}, {"name": "get_RemainingArguments", "returnType": "System.Collections.Generic.List`1<System.String>", "parameterTypes": [], "isStatic": false}, {"name": "get_ApplicationArguments", "returnType": "System.Collections.Generic.List`1<System.String>", "parameterTypes": [], "isStatic": false}, {"name": "get_IsShowingInformation", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "get_Invoke", "returnType": "System.Func`2<System.String[],System.Int32>", "parameterTypes": [], "isStatic": false}, {"name": "set_Invoke", "returnType": "System.Void", "parameterTypes": [["value", "System.Func`2<System.String[],System.Int32>"]], "isStatic": false}, {"name": "get_LongVersionGetter", "returnType": "System.Func`1<System.String>", "parameterTypes": [], "isStatic": false}, {"name": "set_LongVersionGetter", "returnType": "System.Void", "parameterTypes": [["value", "System.Func`1<System.String>"]], "isStatic": false}, {"name": "get_ShortVersionGetter", "returnType": "System.Func`1<System.String>", "parameterTypes": [], "isStatic": false}, {"name": "set_ShortVersionGetter", "returnType": "System.Void", "parameterTypes": [["value", "System.Func`1<System.String>"]], "isStatic": false}, {"name": "get_Commands", "returnType": "System.Collections.Generic.List`1<Microsoft.DotNet.Cli.CommandLine.CommandLineApplication>", "parameterTypes": [], "isStatic": false}, {"name": "get_HandleResponseFiles", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_HandleResponseFiles", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_AllowArgumentSeparator", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_AllowArgumentSeparator", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_HandleRemainingArguments", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_HandleRemainingArguments", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_ArgumentSeparatorHelpText", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ArgumentSeparatorHelpText", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "Command", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandLineApplication", "parameterTypes": [["name", "System.String"], ["throwOnUnexpectedArg", "System.Boolean"]], "isStatic": false}, {"name": "Command", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandLineApplication", "parameterTypes": [["name", "System.String"], ["configuration", "System.Action`1<Microsoft.DotNet.Cli.CommandLine.CommandLineApplication>"], ["throwOnUnexpectedArg", "System.Boolean"]], "isStatic": false}, {"name": "Option", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandOption", "parameterTypes": [["template", "System.String"], ["description", "System.String"], ["optionType", "Microsoft.DotNet.Cli.CommandLine.CommandOptionType"]], "isStatic": false}, {"name": "Option", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandOption", "parameterTypes": [["template", "System.String"], ["description", "System.String"], ["optionType", "Microsoft.DotNet.Cli.CommandLine.CommandOptionType"], ["configuration", "System.Action`1<Microsoft.DotNet.Cli.CommandLine.CommandOption>"]], "isStatic": false}, {"name": "Argument", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandArgument", "parameterTypes": [["name", "System.String"], ["description", "System.String"], ["multipleValues", "System.Boolean"]], "isStatic": false}, {"name": "Argument", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandArgument", "parameterTypes": [["name", "System.String"], ["description", "System.String"], ["configuration", "System.Action`1<Microsoft.DotNet.Cli.CommandLine.CommandArgument>"], ["multipleValues", "System.Boolean"]], "isStatic": false}, {"name": "OnExecute", "returnType": "System.Void", "parameterTypes": [["invoke", "System.Func`2<System.String[],System.Int32>"]], "isStatic": false}, {"name": "OnExecute", "returnType": "System.Void", "parameterTypes": [["invoke", "System.Func`2<System.String[],System.Threading.Tasks.Task`1<System.Int32>>"]], "isStatic": false}, {"name": "Execute", "returnType": "System.Int32", "parameterTypes": [["args", "System.String[]"]], "isStatic": false}, {"name": "HelpOption", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandOption", "parameterTypes": [["template", "System.String"]], "isStatic": false}, {"name": "VersionOption", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandOption", "parameterTypes": [["template", "System.String"], ["shortFormVersion", "System.String"], ["longFormVersion", "System.String"]], "isStatic": false}, {"name": "VersionOption", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandOption", "parameterTypes": [["template", "System.String"], ["shortFormVersionGetter", "System.Func`1<System.String>"], ["longFormVersionGetter", "System.Func`1<System.String>"]], "isStatic": false}, {"name": "ShowHint", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "ShowHelp", "returnType": "System.Void", "parameterTypes": [["commandName", "System.String"]], "isStatic": false}, {"name": "ShowVersion", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "GetFullNameAndVersion", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "ShowRootCommandFullNameAndVersion", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.DotNet.Cli.CommandLine.CommandLineApplicationExtensions", "methods": [{"name": "Option", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandOption", "parameterTypes": [["command", "Microsoft.DotNet.Cli.CommandLine.CommandLineApplication"], ["template", "System.String"], ["description", "System.String"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.DotNet.Cli.CommandLine.CommandOption", "methods": [{"name": "get_Template", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Template", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ShortName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ShortName", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_LongName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_LongName", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_SymbolName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_SymbolName", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ValueName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ValueName", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Description", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Description", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Values", "returnType": "System.Collections.Generic.List`1<System.String>", "parameterTypes": [], "isStatic": false}, {"name": "get_BoolValue", "returnType": "System.Nullable`1<System.Boolean>", "parameterTypes": [], "isStatic": false}, {"name": "get_OptionType", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandOptionType", "parameterTypes": [], "isStatic": false}, {"name": "TryParse", "returnType": "System.Boolean", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "HasValue", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "Value", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.DotNet.Cli.CommandLine.CommandOptionType", "methods": [], "fields": []}, {"name": "Microsoft.DotNet.Cli.CommandLine.CommandParsingException", "methods": [{"name": "get_Command", "returnType": "Microsoft.DotNet.Cli.CommandLine.CommandLineApplication", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.EntityFrameworkCore": [{"name": "Microsoft.EntityFrameworkCore.ScaffoldingModelExtensions", "methods": [{"name": "IsSimpleManyToManyJoinEntityType", "returnType": "System.Boolean", "parameterTypes": [["entityType", "Microsoft.EntityFrameworkCore.Metadata.IEntityType"]], "isStatic": true}, {"name": "IsLeftNavigation", "returnType": "System.Boolean", "parameterTypes": [["skipNavigation", "Microsoft.EntityFrameworkCore.Metadata.ISkipNavigation"]], "isStatic": true}, {"name": "GetDbSetName", "returnType": "System.String", "parameterTypes": [["entityType", "Microsoft.EntityFrameworkCore.Metadata.IReadOnlyEntityType"]], "isStatic": true}, {"name": "IsHandledByConvention", "returnType": "System.Boolean", "parameterTypes": [["key", "Microsoft.EntityFrameworkCore.Metadata.IKey"]], "isStatic": true}, {"name": "IsHandledByDataAnnotations", "returnType": "System.Boolean", "parameterTypes": [["index", "Microsoft.EntityFrameworkCore.Metadata.IIndex"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}, {"name": "GetDataAnnotations", "returnType": "System.Collections.Generic.IEnumerable`1<Microsoft.EntityFrameworkCore.Design.AttributeCodeFragment>", "parameterTypes": [["entityType", "Microsoft.EntityFrameworkCore.Metadata.IEntityType"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}, {"name": "GetDataAnnotations", "returnType": "System.Collections.Generic.IEnumerable`1<Microsoft.EntityFrameworkCore.Design.AttributeCodeFragment>", "parameterTypes": [["property", "Microsoft.EntityFrameworkCore.Metadata.IProperty"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}, {"name": "GetDataAnnotations", "returnType": "System.Collections.Generic.IEnumerable`1<Microsoft.EntityFrameworkCore.Design.AttributeCodeFragment>", "parameterTypes": [["navigation", "Microsoft.EntityFrameworkCore.Metadata.INavigation"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}, {"name": "GetDataAnnotations", "returnType": "System.Collections.Generic.IEnumerable`1<Microsoft.EntityFrameworkCore.Design.AttributeCodeFragment>", "parameterTypes": [["skipNavigation", "Microsoft.EntityFrameworkCore.Metadata.ISkipNavigation"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}, {"name": "GetFluentApiCalls", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}, {"name": "GetFluentApiCalls", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [["entityType", "Microsoft.EntityFrameworkCore.Metadata.IEntityType"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}, {"name": "GetFluentApiCalls", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [["key", "Microsoft.EntityFrameworkCore.Metadata.IKey"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}, {"name": "GetFluentApiCalls", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [["index", "Microsoft.EntityFrameworkCore.Metadata.IIndex"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}, {"name": "GetFluentApiCalls", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [["property", "Microsoft.EntityFrameworkCore.Metadata.IProperty"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}, {"name": "GetFluentApiCalls", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [["foreignKey", "Microsoft.EntityFrameworkCore.Metadata.IForeignKey"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"], ["useStrings", "System.Boolean"]], "isStatic": true}, {"name": "GetFluentApiCalls", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [["sequence", "Microsoft.EntityFrameworkCore.Metadata.ISequence"], ["annotationCodeGenerator", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.EnumerableMethods", "methods": [{"name": "get_AggregateWithSeedSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_All", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_AnyWithoutPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_AnyWithPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_AsEnumerable", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Cast", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Concat", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Contains", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_CountWithoutPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_CountWithPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_DefaultIfEmptyWithoutArgument", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_DefaultIfEmptyWithArgument", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Distinct", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_ElementAt", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_ElementAtOrDefault", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Except", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_FirstWithoutPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_FirstWithPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_FirstOrDefaultWithoutPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_FirstOrDefaultWithPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_GroupByWithKeySelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_GroupByWithKeyElementSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_GroupByWithKeyElementResultSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_GroupByWithKeyResultSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_GroupJoin", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Intersect", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Join", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Join<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_LastWithoutPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_LastWithPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_LastOrDefaultWithoutPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_LastOrDefaultWithPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_LongCountWithoutPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_LongCountWithPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_MaxWithoutSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_MaxWithSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_MinWithoutSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_MinWithSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_OfType", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_OrderBy", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_OrderByDescending", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Reverse", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Select", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_SelectWithOrdinal", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_SelectManyWithoutCollectionSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_SelectManyWithCollectionSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_SequenceEqual", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_SingleWithoutPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_SingleWithPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_SingleOrDefaultWithoutPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_SingleOrDefaultWithPredicate", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Skip", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Take", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_<PERSON><PERSON><PERSON>e", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_ThenBy", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_ThenByDescending", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_ToArray", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_ToList", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Union", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_Where", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "get_ZipWithSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [], "isStatic": true}, {"name": "GetSumWithSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetAverageWithSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetMaxWithoutSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetMaxWithSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetMinWithoutSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [["type", "System.Type"]], "isStatic": true}, {"name": "GetMinWithSelector", "returnType": "System.Reflection.MethodInfo", "parameterTypes": [["type", "System.Type"]], "isStatic": true}], "fields": []}], "Microsoft.EntityFrameworkCore.Utilities": [{"name": "Microsoft.EntityFrameworkCore.Utilities.Check", "methods": [{"name": "NotNull", "returnType": "T", "parameterTypes": [["value", "T"], ["parameterName", "System.String"]], "isStatic": true}, {"name": "NotEmpty", "returnType": "System.Collections.Generic.IReadOnlyList`1<T>", "parameterTypes": [["value", "System.Collections.Generic.IReadOnlyList`1<T>"], ["parameterName", "System.String"]], "isStatic": true}, {"name": "NotEmpty", "returnType": "System.String", "parameterTypes": [["value", "System.String"], ["parameterName", "System.String"]], "isStatic": true}, {"name": "NullButNotEmpty", "returnType": "System.String", "parameterTypes": [["value", "System.String"], ["parameterName", "System.String"]], "isStatic": true}, {"name": "Has<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Collections.Generic.IReadOnlyList`1<T>", "parameterTypes": [["value", "System.Collections.Generic.IReadOnlyList`1<T>"], ["parameterName", "System.String"]], "isStatic": true}, {"name": "HasNoEmptyElements", "returnType": "System.Collections.Generic.IReadOnlyList`1<System.String>", "parameterTypes": [["value", "System.Collections.Generic.IReadOnlyList`1<System.String>"], ["parameterName", "System.String"]], "isStatic": true}, {"name": "DebugAssert", "returnType": "System.Void", "parameterTypes": [["condition", "System.Boolean"], ["message", "System.String"]], "isStatic": true}, {"name": "DebugFail", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Utilities.DictionaryExtensions", "methods": [{"name": "GetOrAddNew", "returnType": "TValue", "parameterTypes": [["source", "System.Collections.Generic.IDictionary`2<<PERSON><PERSON><PERSON>,TValue>"], ["key", "<PERSON><PERSON><PERSON>"]], "isStatic": true}, {"name": "Find", "returnType": "TValue", "parameterTypes": [["source", "System.Collections.Generic.IReadOnlyDictionary`2<<PERSON><PERSON><PERSON>,TV<PERSON>ue>"], ["key", "<PERSON><PERSON><PERSON>"]], "isStatic": true}, {"name": "TryGetAndRemove", "returnType": "System.Boolean", "parameterTypes": [["source", "System.Collections.Generic.IDictionary`2<<PERSON><PERSON><PERSON>,TValue>"], ["key", "<PERSON><PERSON><PERSON>"], ["value", "TReturn&"]], "isStatic": true}, {"name": "Remove", "returnType": "System.Void", "parameterTypes": [["source", "System.Collections.Generic.IDictionary`2<<PERSON><PERSON><PERSON>,TValue>"], ["predicate", "System.Func`3<<PERSON><PERSON><PERSON>,<PERSON><PERSON>ue,System.Boolean>"]], "isStatic": true}, {"name": "Remove", "returnType": "System.Void", "parameterTypes": [["source", "System.Collections.Generic.IDictionary`2<<PERSON><PERSON><PERSON>,TValue>"], ["predicate", "System.Func`4<TKey,TValue,TState,System.Boolean>"], ["state", "TState"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Utilities.DisposableExtensions", "methods": [{"name": "DisposeAsyncIfAvailable", "returnType": "System.Threading.Tasks.ValueTask", "parameterTypes": [["disposable", "System.IDisposable"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Utilities.EnumerableExtensions", "methods": [{"name": "OrderByOrdinal", "returnType": "System.Linq.IOrderedEnumerable`1<TSource>", "parameterTypes": [["source", "System.Collections.Generic.IEnumerable`1<TSource>"], ["keySelector", "System.Func`2<TSource,System.String>"]], "isStatic": true}, {"name": "Distinct", "returnType": "System.Collections.Generic.IEnumerable`1<T>", "parameterTypes": [["source", "System.Collections.Generic.IEnumerable`1<T>"], ["comparer", "System.Func`3<T,<PERSON>,<PERSON><PERSON>Boolean>"]], "isStatic": true}, {"name": "Join", "returnType": "System.String", "parameterTypes": [["source", "System.Collections.Generic.IEnumerable`1<System.Object>"], ["separator", "System.String"]], "isStatic": true}, {"name": "StructuralSequenceEqual", "returnType": "System.Boolean", "parameterTypes": [["first", "System.Collections.Generic.IEnumerable`1<TSource>"], ["second", "System.Collections.Generic.IEnumerable`1<TSource>"]], "isStatic": true}, {"name": "StartsWith", "returnType": "System.Boolean", "parameterTypes": [["first", "System.Collections.Generic.IEnumerable`1<TSource>"], ["second", "System.Collections.Generic.IEnumerable`1<TSource>"]], "isStatic": true}, {"name": "IndexOf", "returnType": "System.Int32", "parameterTypes": [["source", "System.Collections.Generic.IEnumerable`1<T>"], ["item", "T"]], "isStatic": true}, {"name": "IndexOf", "returnType": "System.Int32", "parameterTypes": [["source", "System.Collections.Generic.IEnumerable`1<T>"], ["item", "T"], ["comparer", "System.Collections.Generic.IEqualityComparer`1<T>"]], "isStatic": true}, {"name": "FirstOr", "returnType": "T", "parameterTypes": [["source", "System.Collections.Generic.IEnumerable`1<T>"], ["alternate", "T"]], "isStatic": true}, {"name": "FirstOr", "returnType": "T", "parameterTypes": [["source", "System.Collections.Generic.IEnumerable`1<T>"], ["predicate", "System.Func`2<<PERSON>,<PERSON><PERSON>Bo<PERSON>>"], ["alternate", "T"]], "isStatic": true}, {"name": "Any", "returnType": "System.Boolean", "parameterTypes": [["source", "System.Collections.IEnumerable"]], "isStatic": true}, {"name": "ToListAsync", "returnType": "System.Threading.Tasks.Task`1<System.Collections.Generic.List`1<TSource>>", "parameterTypes": [["source", "System.Collections.Generic.IAsyncEnumerable`1<TSource>"], ["cancellationToken", "System.Threading.CancellationToken"]], "isStatic": true}, {"name": "ToList", "returnType": "System.Collections.Generic.List`1<TSource>", "parameterTypes": [["source", "System.Collections.IEnumerable"]], "isStatic": true}, {"name": "Format", "returnType": "System.String", "parameterTypes": [["strings", "System.Collections.Generic.IEnumerable`1<System.String>"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Utilities.Graph`1", "methods": [{"name": "get_Vertices", "returnType": "System.Collections.Generic.IEnumerable`1<TVertex>", "parameterTypes": [], "isStatic": false}, {"name": "Clear", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "GetOutgoingNeighbors", "returnType": "System.Collections.Generic.IEnumerable`1<TVertex>", "parameterTypes": [["from", "TVertex"]], "isStatic": false}, {"name": "GetIncomingNeighbors", "returnType": "System.Collections.Generic.IEnumerable`1<TVertex>", "parameterTypes": [["to", "TVertex"]], "isStatic": false}, {"name": "GetUnreachableVertices", "returnType": "System.Collections.Generic.ISet`1<TVertex>", "parameterTypes": [["roots", "System.Collections.Generic.IReadOnlyList`1<TVertex>"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Utilities.HashHelpers", "methods": [{"name": "IsPrime", "returnType": "System.Boolean", "parameterTypes": [["candidate", "System.Int32"]], "isStatic": true}, {"name": "GetPrime", "returnType": "System.Int32", "parameterTypes": [["min", "System.Int32"]], "isStatic": true}, {"name": "ExpandPrime", "returnType": "System.Int32", "parameterTypes": [["oldSize", "System.Int32"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Utilities.Multigraph`2", "methods": [{"name": "Get<PERSON>dges", "returnType": "System.Collections.Generic.IEnumerable`1<TEdge>", "parameterTypes": [["from", "TVertex"], ["to", "TVertex"]], "isStatic": false}, {"name": "AddVertex", "returnType": "System.Void", "parameterTypes": [["vertex", "TVertex"]], "isStatic": false}, {"name": "AddVertices", "returnType": "System.Void", "parameterTypes": [["vertices", "System.Collections.Generic.IEnumerable`1<TVertex>"]], "isStatic": false}, {"name": "AddEdge", "returnType": "System.Void", "parameterTypes": [["from", "TVertex"], ["to", "TVertex"], ["payload", "TEdge"], ["requiresBatchingBoundary", "System.Boolean"]], "isStatic": false}, {"name": "Clear", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "TopologicalSort", "returnType": "System.Collections.Generic.IReadOnlyList`1<TVertex>", "parameterTypes": [], "isStatic": false}, {"name": "TopologicalSort", "returnType": "System.Collections.Generic.IReadOnlyList`1<TVertex>", "parameterTypes": [["tryBreakEdge", "System.Func`4<TVertex,TVertex,System.Collections.Generic.IEnumerable`1<TEdge>,System.Boolean>"]], "isStatic": false}, {"name": "TopologicalSort", "returnType": "System.Collections.Generic.IReadOnlyList`1<TVertex>", "parameterTypes": [["formatCycle", "System.Func`2<System.Collections.Generic.IEnumerable`1<System.Tuple`3<TVertex,TVertex,System.Collections.Generic.IEnumerable`1<TEdge>>>,System.String>"]], "isStatic": false}, {"name": "TopologicalSort", "returnType": "System.Collections.Generic.IReadOnlyList`1<TVertex>", "parameterTypes": [["tryBreakEdge", "System.Func`4<TVertex,TVertex,System.Collections.Generic.IEnumerable`1<TEdge>,System.Boolean>"], ["formatCycle", "System.Func`2<System.Collections.Generic.IReadOnlyList`1<System.Tuple`3<TVertex,TVertex,System.Collections.Generic.IEnumerable`1<TEdge>>>,System.String>"], ["formatException", "System.Func`2<System.String,System.String>"]], "isStatic": false}, {"name": "BatchingTopologicalSort", "returnType": "System.Collections.Generic.IReadOnlyList`1<System.Collections.Generic.List`1<TVertex>>", "parameterTypes": [], "isStatic": false}, {"name": "BatchingTopologicalSort", "returnType": "System.Collections.Generic.IReadOnlyList`1<System.Collections.Generic.List`1<TVertex>>", "parameterTypes": [["canBreakEdges", "System.Func`4<TVertex,TVertex,System.Collections.Generic.IEnumerable`1<TEdge>,System.Boolean>"], ["formatCycle", "System.Func`2<System.Collections.Generic.IReadOnlyList`1<System.Tuple`3<TVertex,TVertex,System.Collections.Generic.IEnumerable`1<TEdge>>>,System.String>"], ["formatException", "System.Func`2<System.String,System.String>"]], "isStatic": false}, {"name": "get_Vertices", "returnType": "System.Collections.Generic.IEnumerable`1<TVertex>", "parameterTypes": [], "isStatic": false}, {"name": "GetOutgoingNeighbors", "returnType": "System.Collections.Generic.IEnumerable`1<TVertex>", "parameterTypes": [["from", "TVertex"]], "isStatic": false}, {"name": "GetIncomingNeighbors", "returnType": "System.Collections.Generic.IEnumerable`1<TVertex>", "parameterTypes": [["to", "TVertex"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Utilities.SharedStopwatch", "methods": [{"name": "get_Elapsed", "returnType": "System.TimeSpan", "parameterTypes": [], "isStatic": false}, {"name": "StartNew", "returnType": "Microsoft.EntityFrameworkCore.Utilities.SharedStopwatch", "parameterTypes": [], "isStatic": true}], "fields": []}], "Microsoft.EntityFrameworkCore.Tools": [{"name": "Microsoft.EntityFrameworkCore.Tools.Reporter", "methods": [{"name": "get_IsVer<PERSON>e", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": true}, {"name": "set_IsVerbose", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": true}, {"name": "get_NoColor", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": true}, {"name": "set_NoColor", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": true}, {"name": "get_PrefixOutput", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": true}, {"name": "set_PrefixOutput", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": true}, {"name": "Colorize", "returnType": "System.String", "parameterTypes": [["value", "System.String"], ["colorizeFunc", "System.Func`2<System.String,System.String>"]], "isStatic": true}, {"name": "WriteError", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": true}, {"name": "WriteWarning", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": true}, {"name": "WriteInformation", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": true}, {"name": "WriteData", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": true}, {"name": "WriteVerbose", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Tools.AnsiConstants", "methods": [], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Tools.AnsiConsole", "methods": [{"name": "WriteLine", "returnType": "System.Void", "parameterTypes": [["text", "System.String"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Tools.AnsiTextWriter", "methods": [{"name": "WriteLine", "returnType": "System.Void", "parameterTypes": [["text", "System.String"]], "isStatic": false}], "fields": []}], "Microsoft.EntityFrameworkCore.Query.Internal": [{"name": "Microsoft.EntityFrameworkCore.Query.Internal.ILinqToCSharpSyntaxTranslator", "methods": [{"name": "TranslateStatement", "returnType": "Microsoft.CodeAnalysis.SyntaxNode", "parameterTypes": [["node", "System.Linq.Expressions.Expression"], ["collectedNamespaces", "System.Collections.Generic.ISet`1<System.String>"]], "isStatic": false}, {"name": "TranslateExpression", "returnType": "Microsoft.CodeAnalysis.SyntaxNode", "parameterTypes": [["node", "System.Linq.Expressions.Expression"], ["collectedNamespaces", "System.Collections.Generic.ISet`1<System.String>"]], "isStatic": false}, {"name": "get_CapturedVariables", "returnType": "System.Collections.Generic.IReadOnlySet`1<System.Linq.Expressions.ParameterExpression>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Query.Internal.LinqToCSharpSyntaxTranslator", "methods": [{"name": "get_CapturedVariables", "returnType": "System.Collections.Generic.IReadOnlySet`1<System.Linq.Expressions.ParameterExpression>", "parameterTypes": [], "isStatic": false}, {"name": "TranslateStatement", "returnType": "Microsoft.CodeAnalysis.SyntaxNode", "parameterTypes": [["node", "System.Linq.Expressions.Expression"], ["collectedNamespaces", "System.Collections.Generic.ISet`1<System.String>"]], "isStatic": false}, {"name": "TranslateExpression", "returnType": "Microsoft.CodeAnalysis.SyntaxNode", "parameterTypes": [["node", "System.Linq.Expressions.Expression"], ["collectedNamespaces", "System.Collections.Generic.ISet`1<System.String>"]], "isStatic": false}, {"name": "Visit", "returnType": "System.Linq.Expressions.Expression", "parameterTypes": [["node", "System.Linq.Expressions.Expression"]], "isStatic": false}], "fields": []}], "Microsoft.EntityFrameworkCore.Internal": [{"name": "Microsoft.EntityFrameworkCore.Internal.DesignStrings", "methods": [{"name": "BadConnection", "returnType": "System.String", "parameterTypes": [["exceptionMessage", "System.Object"]], "isStatic": true}, {"name": "BadSequenceType", "returnType": "System.String", "parameterTypes": [["sequenceName", "System.Object"], ["typeName", "System.Object"]], "isStatic": true}, {"name": "get_BundleFullName", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "CannotCreateContextInstance", "returnType": "System.String", "parameterTypes": [["contextType", "System.Object"], ["rootException", "System.Object"]], "isStatic": true}, {"name": "CannotFindDbContextTypes", "returnType": "System.String", "parameterTypes": [["rootException", "System.Object"]], "isStatic": true}, {"name": "CannotFindDesignTimeProviderAssemblyAttribute", "returnType": "System.String", "parameterTypes": [["runtimeProviderAssemblyName", "System.Object"]], "isStatic": true}, {"name": "CannotFindRuntimeProviderAssembly", "returnType": "System.String", "parameterTypes": [["assemblyName", "System.Object"]], "isStatic": true}, {"name": "CannotFindTypeMappingForColumn", "returnType": "System.String", "parameterTypes": [["columnName", "System.Object"], ["dateType", "System.Object"]], "isStatic": true}, {"name": "get_CannotGenerateTypeQualifiedMethodCall", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_CircularBaseClassDependency", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "CompiledModelConstructorBinding", "returnType": "System.String", "parameterTypes": [["entityType", "System.Object"], ["customize", "System.Object"], ["className", "System.Object"]], "isStatic": true}, {"name": "CompiledModelCustomCacheKeyFactory", "returnType": "System.String", "parameterTypes": [["factoryType", "System.Object"]], "isStatic": true}, {"name": "CompiledModelDefiningQuery", "returnType": "System.String", "parameterTypes": [["entityType", "System.Object"]], "isStatic": true}, {"name": "CompiledModelGenerated", "returnType": "System.String", "parameterTypes": [["optionsCall", "System.Object"]], "isStatic": true}, {"name": "CompiledModelQueryFilter", "returnType": "System.String", "parameterTypes": [["entityType", "System.Object"]], "isStatic": true}, {"name": "CompiledModelValueGenerator", "returnType": "System.String", "parameterTypes": [["entityType", "System.Object"], ["property", "System.Object"], ["method", "System.Object"]], "isStatic": true}, {"name": "ConflictingContextAndMigrationName", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "get_ConnectionDescription", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "ContextClassNotValidCSharpIdentifier", "returnType": "System.String", "parameterTypes": [["contextClassName", "System.Object"]], "isStatic": true}, {"name": "DatabaseDropped", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "get_DestructiveOperation", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_Done", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "DroppingDatabase", "returnType": "System.String", "parameterTypes": [["database", "System.Object"], ["dataSource", "System.Object"]], "isStatic": true}, {"name": "DuplicateMigrationName", "returnType": "System.String", "parameterTypes": [["migrationName", "System.Object"]], "isStatic": true}, {"name": "EncodingIgnored", "returnType": "System.String", "parameterTypes": [["encoding", "System.Object"]], "isStatic": true}, {"name": "ErrorConnecting", "returnType": "System.String", "parameterTypes": [["message", "System.Object"]], "isStatic": true}, {"name": "ErrorGeneratingOutput", "returnType": "System.String", "parameterTypes": [["inputFile", "System.Object"]], "isStatic": true}, {"name": "ExistingFiles", "returnType": "System.String", "parameterTypes": [["outputDirectoryName", "System.Object"], ["existingFiles", "System.Object"]], "isStatic": true}, {"name": "get_FindingContextFactories", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_FindingContexts", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "FindingDesignTimeServices", "returnType": "System.String", "parameterTypes": [["startupAssembly", "System.Object"]], "isStatic": true}, {"name": "get_FindingHostingServices", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "FindingProviderServices", "returnType": "System.String", "parameterTypes": [["provider", "System.Object"]], "isStatic": true}, {"name": "get_FindingReferencedContexts", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "FindingReferencedServices", "returnType": "System.String", "parameterTypes": [["startupAssembly", "System.Object"]], "isStatic": true}, {"name": "FindingServiceProvider", "returnType": "System.String", "parameterTypes": [["startupAssembly", "System.Object"]], "isStatic": true}, {"name": "ForceRemoveMigration", "returnType": "System.String", "parameterTypes": [["name", "System.Object"], ["error", "System.Object"]], "isStatic": true}, {"name": "ForeignKeyPrincipalEndContainsNullableColumns", "returnType": "System.String", "parameterTypes": [["foreignKeyName", "System.Object"], ["indexName", "System.Object"], ["columnNames", "System.Object"]], "isStatic": true}, {"name": "ForeignKeyScaffoldErrorPrincipalKeyNotFound", "returnType": "System.String", "parameterTypes": [["foreignKeyName", "System.Object"], ["columnsList", "System.Object"], ["principalEntityType", "System.Object"]], "isStatic": true}, {"name": "ForeignKeyScaffoldErrorPrincipalTableNotFound", "returnType": "System.String", "parameterTypes": [["foreignKeyName", "System.Object"]], "isStatic": true}, {"name": "ForeignKeyScaffoldErrorPrincipalTableScaffoldingError", "returnType": "System.String", "parameterTypes": [["foreignKeyName", "System.Object"], ["principalTableName", "System.Object"]], "isStatic": true}, {"name": "ForeignKeyScaffoldErrorPropertyNotFound", "returnType": "System.String", "parameterTypes": [["foreignKeyName", "System.Object"], ["columnNames", "System.Object"]], "isStatic": true}, {"name": "ForeignKeyWithSameFacetsExists", "returnType": "System.String", "parameterTypes": [["foreignKeyName", "System.Object"], ["existingForeignKey", "System.Object"]], "isStatic": true}, {"name": "ForeignMigrations", "returnType": "System.String", "parameterTypes": [["migrationsNamespace", "System.Object"]], "isStatic": true}, {"name": "FoundContextFactory", "returnType": "System.String", "parameterTypes": [["factory", "System.Object"]], "isStatic": true}, {"name": "FoundDbContext", "returnType": "System.String", "parameterTypes": [["contextType", "System.Object"]], "isStatic": true}, {"name": "InvokeCreateHostBuilderFailed", "returnType": "System.String", "parameterTypes": [["error", "System.Object"]], "isStatic": true}, {"name": "LiteralExpressionNotSupported", "returnType": "System.String", "parameterTypes": [["expression", "System.Object"], ["type", "System.Object"]], "isStatic": true}, {"name": "get_MalformedCreateHostBuilder", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_ManuallyDeleted", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_MigrationDescription", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "MigrationsAssemblyMismatch", "returnType": "System.String", "parameterTypes": [["assembly", "System.Object"], ["migrationsAssembly", "System.Object"]], "isStatic": true}, {"name": "MultipleAnnotationConflict", "returnType": "System.String", "parameterTypes": [["annotationName", "System.Object"]], "isStatic": true}, {"name": "get_MultipleContexts", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "MultipleContextsWithName", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "MultipleContextsWithQualifiedName", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "get_NoColorDescription", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "NoContext", "returnType": "System.String", "parameterTypes": [["assembly", "System.Object"]], "isStatic": true}, {"name": "get_NoContextTemplate", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_NoContextTemplateButConfiguration", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "NoContextWithName", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "get_NoCreateHostBuilder", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_NoDesignTimeServices", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "NoLanguageService", "returnType": "System.String", "parameterTypes": [["language", "System.Object"], ["service", "System.Object"]], "isStatic": true}, {"name": "NoMigrationFile", "returnType": "System.String", "parameterTypes": [["file", "System.Object"], ["migrationClass", "System.Object"]], "isStatic": true}, {"name": "NoMigrationMetadataFile", "returnType": "System.String", "parameterTypes": [["file", "System.Object"]], "isStatic": true}, {"name": "NonNullableBoooleanColumnHasDefaultConstraint", "returnType": "System.String", "parameterTypes": [["columnName", "System.Object"]], "isStatic": true}, {"name": "NonRelationalProvider", "returnType": "System.String", "parameterTypes": [["provider", "System.Object"]], "isStatic": true}, {"name": "get_NoPendingModelChanges", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_NoReferencedServices", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_NoRelationalConnection", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_NoServiceProvider", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_NoSnapshot", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "NoSnapshotFile", "returnType": "System.String", "parameterTypes": [["file", "System.Object"], ["snapshotClass", "System.Object"]], "isStatic": true}, {"name": "NotExistDatabase", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "get_PendingModelChanges", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_PrefixDescription", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "PrimaryKeyErrorPropertyNotFound", "returnType": "System.String", "parameterTypes": [["tableName", "System.Object"], ["columnNames", "System.Object"]], "isStatic": true}, {"name": "ProviderReturnedNullModel", "returnType": "System.String", "parameterTypes": [["providerTypeName", "System.Object"]], "isStatic": true}, {"name": "ReadOnlyFiles", "returnType": "System.String", "parameterTypes": [["outputDirectoryName", "System.Object"], ["readOnlyFiles", "System.Object"]], "isStatic": true}, {"name": "RemovingMigration", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "get_RemovingSnapshot", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "ReusingNamespace", "returnType": "System.String", "parameterTypes": [["type", "System.Object"]], "isStatic": true}, {"name": "ReusingSnapshotName", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "get_RevertingSnapshot", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "RevertMigration", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "get_SensitiveInformationWarning", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "get_SequencesRequireName", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "UnableToGenerateEntityType", "returnType": "System.String", "parameterTypes": [["tableName", "System.Object"]], "isStatic": true}, {"name": "UnableToScaffoldIndexMissingProperty", "returnType": "System.String", "parameterTypes": [["indexName", "System.Object"], ["columnNames", "System.Object"]], "isStatic": true}, {"name": "UnhandledEnumValue", "returnType": "System.String", "parameterTypes": [["enumValue", "System.Object"]], "isStatic": true}, {"name": "UnknownDirectiveProcessor", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "System.String", "parameterTypes": [["literalType", "System.Object"]], "isStatic": true}, {"name": "UnknownOperation", "returnType": "System.String", "parameterTypes": [["operationType", "System.Object"]], "isStatic": true}, {"name": "UnreferencedAssembly", "returnType": "System.String", "parameterTypes": [["assembly", "System.Object"], ["startupProject", "System.Object"]], "isStatic": true}, {"name": "UseContext", "returnType": "System.String", "parameterTypes": [["name", "System.Object"]], "isStatic": true}, {"name": "UsingDbContextFactory", "returnType": "System.String", "parameterTypes": [["factory", "System.Object"]], "isStatic": true}, {"name": "UsingDesignTimeServices", "returnType": "System.String", "parameterTypes": [["designTimeServices", "System.Object"]], "isStatic": true}, {"name": "UsingEnvironment", "returnType": "System.String", "parameterTypes": [["environment", "System.Object"]], "isStatic": true}, {"name": "get_UsingHostingServices", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "UsingProviderServices", "returnType": "System.String", "parameterTypes": [["provider", "System.Object"]], "isStatic": true}, {"name": "UsingReferencedServices", "returnType": "System.String", "parameterTypes": [["referencedAssembly", "System.Object"]], "isStatic": true}, {"name": "get_VerboseDescription", "returnType": "System.String", "parameterTypes": [], "isStatic": true}, {"name": "VersionMismatch", "returnType": "System.String", "parameterTypes": [["toolsVersion", "System.Object"], ["runtimeVersion", "System.Object"]], "isStatic": true}, {"name": "WritingMigration", "returnType": "System.String", "parameterTypes": [["file", "System.Object"]], "isStatic": true}, {"name": "WritingSnapshot", "returnType": "System.String", "parameterTypes": [["file", "System.Object"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Internal.NonCapturingLazyInitializer", "methods": [{"name": "EnsureInitialized", "returnType": "TValue", "parameterTypes": [["target", "TValue&"], ["param", "TParam"], ["valueFactory", "System.Func`2<TParam,TValue>"]], "isStatic": true}, {"name": "EnsureInitialized", "returnType": "TValue", "parameterTypes": [["target", "TValue&"], ["param1", "TParam1"], ["param2", "TParam2"], ["valueFactory", "System.Func`3<TParam1,TParam2,TValue>"]], "isStatic": true}, {"name": "EnsureInitialized", "returnType": "TValue", "parameterTypes": [["target", "TValue&"], ["param1", "TParam1"], ["param2", "TParam2"], ["param3", "TParam3"], ["valueFactory", "System.Func`4<TParam1,TParam2,TParam3,TValue>"]], "isStatic": true}, {"name": "EnsureInitialized", "returnType": "TValue", "parameterTypes": [["target", "TValue&"], ["initialized", "System.Boolean&"], ["param", "TParam"], ["valueFactory", "System.Func`2<TParam,TValue>"]], "isStatic": true}, {"name": "EnsureInitialized", "returnType": "TValue", "parameterTypes": [["target", "TValue&"], ["value", "TValue"]], "isStatic": true}, {"name": "EnsureInitialized", "returnType": "TValue", "parameterTypes": [["target", "TValue&"], ["param", "TParam"], ["valueFactory", "System.Action`1<TParam>"]], "isStatic": true}], "fields": []}], "Microsoft.EntityFrameworkCore.Migrations.Internal": [{"name": "Microsoft.EntityFrameworkCore.Migrations.Internal.ISnapshotModelProcessor", "methods": [{"name": "Process", "returnType": "Microsoft.EntityFrameworkCore.Metadata.IModel", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IReadOnlyModel"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Internal.MigrationsCodeGeneratorSelector", "methods": [], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Internal.SnapshotModelProcessor", "methods": [{"name": "Process", "returnType": "Microsoft.EntityFrameworkCore.Metadata.IModel", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IReadOnlyModel"]], "isStatic": false}], "fields": []}], "Microsoft.EntityFrameworkCore.Migrations.Design": [{"name": "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationOperationGenerator", "methods": [{"name": "Generate", "returnType": "System.Void", "parameterTypes": [["builderName", "System.String"], ["operations", "System.Collections.Generic.IReadOnlyList`1<Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation>"], ["builder", "Microsoft.EntityFrameworkCore.Infrastructure.IndentedStringBuilder"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationOperationGeneratorDependencies", "methods": [{"name": "get_CSharpHelper", "returnType": "Microsoft.EntityFrameworkCore.Design.ICSharpHelper", "parameterTypes": [], "isStatic": false}, {"name": "set_CSharpHelper", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Design.ICSharpHelper"]], "isStatic": false}, {"name": "ToString", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "op_Inequality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationOperationGeneratorDependencies"], ["right", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationOperationGeneratorDependencies"]], "isStatic": true}, {"name": "op_Equality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationOperationGeneratorDependencies"], ["right", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationOperationGeneratorDependencies"]], "isStatic": true}, {"name": "GetHashCode", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["obj", "System.Object"]], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["other", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationOperationGeneratorDependencies"]], "isStatic": false}, {"name": "<Clone>$", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationOperationGeneratorDependencies", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationsGenerator", "methods": [{"name": "get_FileExtension", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Language", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "GenerateMigration", "returnType": "System.String", "parameterTypes": [["migrationNamespace", "System.String"], ["migrationName", "System.String"], ["upOperations", "System.Collections.Generic.IReadOnlyList`1<Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation>"], ["downOperations", "System.Collections.Generic.IReadOnlyList`1<Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation>"]], "isStatic": false}, {"name": "GenerateMetadata", "returnType": "System.String", "parameterTypes": [["migrationNamespace", "System.String"], ["contextType", "System.Type"], ["migrationName", "System.String"], ["migrationId", "System.String"], ["targetModel", "Microsoft.EntityFrameworkCore.Metadata.IModel"]], "isStatic": false}, {"name": "GenerateSnapshot", "returnType": "System.String", "parameterTypes": [["modelSnapshotNamespace", "System.String"], ["contextType", "System.Type"], ["modelSnapshotName", "System.String"], ["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationsGeneratorDependencies", "methods": [{"name": "get_CSharpHelper", "returnType": "Microsoft.EntityFrameworkCore.Design.ICSharpHelper", "parameterTypes": [], "isStatic": false}, {"name": "set_CSharpHelper", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Design.ICSharpHelper"]], "isStatic": false}, {"name": "get_CSharpMigrationOperationGenerator", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.ICSharpMigrationOperationGenerator", "parameterTypes": [], "isStatic": false}, {"name": "set_CSharpMigrationOperationGenerator", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Migrations.Design.ICSharpMigrationOperationGenerator"]], "isStatic": false}, {"name": "get_CSharpSnapshotGenerator", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.ICSharpSnapshotGenerator", "parameterTypes": [], "isStatic": false}, {"name": "set_CSharpSnapshotGenerator", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Migrations.Design.ICSharpSnapshotGenerator"]], "isStatic": false}, {"name": "ToString", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "op_Inequality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationsGeneratorDependencies"], ["right", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationsGeneratorDependencies"]], "isStatic": true}, {"name": "op_Equality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationsGeneratorDependencies"], ["right", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationsGeneratorDependencies"]], "isStatic": true}, {"name": "GetHashCode", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["obj", "System.Object"]], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["other", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationsGeneratorDependencies"]], "isStatic": false}, {"name": "<Clone>$", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpMigrationsGeneratorDependencies", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpSnapshotGenerator", "methods": [{"name": "Generate", "returnType": "System.Void", "parameterTypes": [["modelBuilderName", "System.String"], ["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["stringBuilder", "Microsoft.EntityFrameworkCore.Infrastructure.IndentedStringBuilder"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpSnapshotGeneratorDependencies", "methods": [{"name": "get_CSharpHelper", "returnType": "Microsoft.EntityFrameworkCore.Design.ICSharpHelper", "parameterTypes": [], "isStatic": false}, {"name": "set_CSharpHelper", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Design.ICSharpHelper"]], "isStatic": false}, {"name": "get_RelationalTypeMappingSource", "returnType": "Microsoft.EntityFrameworkCore.Storage.IRelationalTypeMappingSource", "parameterTypes": [], "isStatic": false}, {"name": "set_RelationalTypeMappingSource", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Storage.IRelationalTypeMappingSource"]], "isStatic": false}, {"name": "get_AnnotationCodeGenerator", "returnType": "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator", "parameterTypes": [], "isStatic": false}, {"name": "set_AnnotationCodeGenerator", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": false}, {"name": "ToString", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "op_Inequality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpSnapshotGeneratorDependencies"], ["right", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpSnapshotGeneratorDependencies"]], "isStatic": true}, {"name": "op_Equality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpSnapshotGeneratorDependencies"], ["right", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpSnapshotGeneratorDependencies"]], "isStatic": true}, {"name": "GetHashCode", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["obj", "System.Object"]], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["other", "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpSnapshotGeneratorDependencies"]], "isStatic": false}, {"name": "<Clone>$", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.CSharpSnapshotGeneratorDependencies", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.ICSharpMigrationOperationGenerator", "methods": [{"name": "Generate", "returnType": "System.Void", "parameterTypes": [["builderName", "System.String"], ["operations", "System.Collections.Generic.IReadOnlyList`1<Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation>"], ["builder", "Microsoft.EntityFrameworkCore.Infrastructure.IndentedStringBuilder"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.ICSharpSnapshotGenerator", "methods": [{"name": "Generate", "returnType": "System.Void", "parameterTypes": [["builderName", "System.String"], ["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["stringBuilder", "Microsoft.EntityFrameworkCore.Infrastructure.IndentedStringBuilder"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.IMigrationsCodeGenerator", "methods": [{"name": "GenerateMetadata", "returnType": "System.String", "parameterTypes": [["migrationNamespace", "System.String"], ["contextType", "System.Type"], ["migrationName", "System.String"], ["migrationId", "System.String"], ["targetModel", "Microsoft.EntityFrameworkCore.Metadata.IModel"]], "isStatic": false}, {"name": "GenerateMigration", "returnType": "System.String", "parameterTypes": [["migrationNamespace", "System.String"], ["migrationName", "System.String"], ["upOperations", "System.Collections.Generic.IReadOnlyList`1<Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation>"], ["downOperations", "System.Collections.Generic.IReadOnlyList`1<Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation>"]], "isStatic": false}, {"name": "GenerateSnapshot", "returnType": "System.String", "parameterTypes": [["modelSnapshotNamespace", "System.String"], ["contextType", "System.Type"], ["modelSnapshotName", "System.String"], ["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"]], "isStatic": false}, {"name": "get_FileExtension", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.IMigrationsCodeGeneratorSelector", "methods": [{"name": "Select", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.IMigrationsCodeGenerator", "parameterTypes": [["language", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.IMigrationsScaffolder", "methods": [{"name": "ScaffoldMigration", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.ScaffoldedMigration", "parameterTypes": [["migrationName", "System.String"], ["rootNamespace", "System.String"], ["subNamespace", "System.String"], ["language", "System.String"]], "isStatic": false}, {"name": "RemoveMigration", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationFiles", "parameterTypes": [["projectDir", "System.String"], ["rootNamespace", "System.String"], ["force", "System.Boolean"], ["language", "System.String"]], "isStatic": false}, {"name": "Save", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationFiles", "parameterTypes": [["projectDir", "System.String"], ["migration", "Microsoft.EntityFrameworkCore.Migrations.Design.ScaffoldedMigration"], ["outputDir", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationFiles", "methods": [{"name": "get_MigrationFile", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_MigrationFile", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_MetadataFile", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_MetadataFile", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_SnapshotFile", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_SnapshotFile", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsBundle", "methods": [{"name": "Execute", "returnType": "System.Int32", "parameterTypes": [["context", "System.String"], ["assembly", "System.Reflection.Assembly"], ["startupAssembly", "System.Reflection.Assembly"], ["args", "System.String[]"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsCodeGenerator", "methods": [{"name": "get_FileExtension", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Language", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "GenerateMigration", "returnType": "System.String", "parameterTypes": [["migrationNamespace", "System.String"], ["migrationName", "System.String"], ["upOperations", "System.Collections.Generic.IReadOnlyList`1<Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation>"], ["downOperations", "System.Collections.Generic.IReadOnlyList`1<Microsoft.EntityFrameworkCore.Migrations.Operations.MigrationOperation>"]], "isStatic": false}, {"name": "GenerateMetadata", "returnType": "System.String", "parameterTypes": [["migrationNamespace", "System.String"], ["contextType", "System.Type"], ["migrationName", "System.String"], ["migrationId", "System.String"], ["targetModel", "Microsoft.EntityFrameworkCore.Metadata.IModel"]], "isStatic": false}, {"name": "GenerateSnapshot", "returnType": "System.String", "parameterTypes": [["modelSnapshotNamespace", "System.String"], ["contextType", "System.Type"], ["modelSnapshotName", "System.String"], ["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsCodeGeneratorDependencies", "methods": [{"name": "get_RelationalTypeMappingSource", "returnType": "Microsoft.EntityFrameworkCore.Storage.IRelationalTypeMappingSource", "parameterTypes": [], "isStatic": false}, {"name": "set_RelationalTypeMappingSource", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Storage.IRelationalTypeMappingSource"]], "isStatic": false}, {"name": "get_AnnotationCodeGenerator", "returnType": "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator", "parameterTypes": [], "isStatic": false}, {"name": "set_AnnotationCodeGenerator", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Design.IAnnotationCodeGenerator"]], "isStatic": false}, {"name": "ToString", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "op_Inequality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsCodeGeneratorDependencies"], ["right", "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsCodeGeneratorDependencies"]], "isStatic": true}, {"name": "op_Equality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsCodeGeneratorDependencies"], ["right", "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsCodeGeneratorDependencies"]], "isStatic": true}, {"name": "GetHashCode", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["obj", "System.Object"]], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["other", "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsCodeGeneratorDependencies"]], "isStatic": false}, {"name": "<Clone>$", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsCodeGeneratorDependencies", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsScaffolder", "methods": [{"name": "ScaffoldMigration", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.ScaffoldedMigration", "parameterTypes": [["migrationName", "System.String"], ["rootNamespace", "System.String"], ["subNamespace", "System.String"]], "isStatic": false}, {"name": "ScaffoldMigration", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.ScaffoldedMigration", "parameterTypes": [["migrationName", "System.String"], ["rootNamespace", "System.String"], ["subNamespace", "System.String"], ["language", "System.String"]], "isStatic": false}, {"name": "RemoveMigration", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationFiles", "parameterTypes": [["projectDir", "System.String"], ["rootNamespace", "System.String"], ["force", "System.Boolean"]], "isStatic": false}, {"name": "RemoveMigration", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationFiles", "parameterTypes": [["projectDir", "System.String"], ["rootNamespace", "System.String"], ["force", "System.Boolean"], ["language", "System.String"]], "isStatic": false}, {"name": "Save", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationFiles", "parameterTypes": [["projectDir", "System.String"], ["migration", "Microsoft.EntityFrameworkCore.Migrations.Design.ScaffoldedMigration"], ["outputDir", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsScaffolderDependencies", "methods": [{"name": "get_CurrentContext", "returnType": "Microsoft.EntityFrameworkCore.Infrastructure.ICurrentDbContext", "parameterTypes": [], "isStatic": false}, {"name": "set_CurrentContext", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Infrastructure.ICurrentDbContext"]], "isStatic": false}, {"name": "get_Model", "returnType": "Microsoft.EntityFrameworkCore.Metadata.IModel", "parameterTypes": [], "isStatic": false}, {"name": "set_Model", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Metadata.IModel"]], "isStatic": false}, {"name": "get_MigrationsAssembly", "returnType": "Microsoft.EntityFrameworkCore.Migrations.IMigrationsAssembly", "parameterTypes": [], "isStatic": false}, {"name": "set_MigrationsAssembly", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Migrations.IMigrationsAssembly"]], "isStatic": false}, {"name": "get_MigrationsM<PERSON><PERSON><PERSON>iff<PERSON>", "returnType": "Microsoft.EntityFrameworkCore.Migrations.IMigrationsModelDiffer", "parameterTypes": [], "isStatic": false}, {"name": "set_Migrations<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Migrations.IMigrationsModelDiffer"]], "isStatic": false}, {"name": "get_MigrationsIdGenerator", "returnType": "Microsoft.EntityFrameworkCore.Migrations.IMigrationsIdGenerator", "parameterTypes": [], "isStatic": false}, {"name": "set_MigrationsIdGenerator", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Migrations.IMigrationsIdGenerator"]], "isStatic": false}, {"name": "get_MigrationsCodeGeneratorSelector", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.IMigrationsCodeGeneratorSelector", "parameterTypes": [], "isStatic": false}, {"name": "set_MigrationsCodeGeneratorSelector", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Migrations.Design.IMigrationsCodeGeneratorSelector"]], "isStatic": false}, {"name": "get_HistoryRepository", "returnType": "Microsoft.EntityFrameworkCore.Migrations.IHistoryRepository", "parameterTypes": [], "isStatic": false}, {"name": "set_HistoryRepository", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Migrations.IHistoryRepository"]], "isStatic": false}, {"name": "get_OperationReporter", "returnType": "Microsoft.EntityFrameworkCore.Design.Internal.IOperationReporter", "parameterTypes": [], "isStatic": false}, {"name": "set_OperationReporter", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Design.Internal.IOperationReporter"]], "isStatic": false}, {"name": "get_DatabaseProvider", "returnType": "Microsoft.EntityFrameworkCore.Storage.IDatabaseProvider", "parameterTypes": [], "isStatic": false}, {"name": "set_DatabaseProvider", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Storage.IDatabaseProvider"]], "isStatic": false}, {"name": "get_SnapshotModelProcessor", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Internal.ISnapshotModelProcessor", "parameterTypes": [], "isStatic": false}, {"name": "set_SnapshotModelProcessor", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Migrations.Internal.ISnapshotModelProcessor"]], "isStatic": false}, {"name": "get_Migrator", "returnType": "Microsoft.EntityFrameworkCore.Migrations.IMigrator", "parameterTypes": [], "isStatic": false}, {"name": "set_Migrator", "returnType": "System.Void modreq(System.Runtime.CompilerServices.IsExternalInit)", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Migrations.IMigrator"]], "isStatic": false}, {"name": "ToString", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "op_Inequality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsScaffolderDependencies"], ["right", "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsScaffolderDependencies"]], "isStatic": true}, {"name": "op_Equality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsScaffolderDependencies"], ["right", "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsScaffolderDependencies"]], "isStatic": true}, {"name": "GetHashCode", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["obj", "System.Object"]], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["other", "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsScaffolderDependencies"]], "isStatic": false}, {"name": "<Clone>$", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationsScaffolderDependencies", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Migrations.Design.ScaffoldedMigration", "methods": [{"name": "get_FileExtension", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_PreviousMigrationId", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_MigrationCode", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_MigrationId", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_MetadataCode", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_MigrationSubNamespace", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_SnapshotCode", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_SnapshotName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_SnapshotSubnamespace", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.EntityFrameworkCore.Metadata.Internal": [{"name": "Microsoft.EntityFrameworkCore.Metadata.Internal.InternalScaffoldingModelExtensions", "methods": [{"name": "GetDatabaseName", "returnType": "System.String", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IReadOnlyModel"]], "isStatic": true}, {"name": "SetDatabaseName", "returnType": "System.Void", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IMutableModel"], ["value", "System.String"]], "isStatic": true}, {"name": "SetDbSetName", "returnType": "System.Void", "parameterTypes": [["entityType", "Microsoft.EntityFrameworkCore.Metadata.IMutableEntityType"], ["value", "System.String"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Metadata.Internal.ScaffoldingAnnotationNames", "methods": [], "fields": []}], "Microsoft.EntityFrameworkCore.Scaffolding": [{"name": "Microsoft.EntityFrameworkCore.Scaffolding.CompiledModelCodeGenerationOptions", "methods": [{"name": "get_ModelNamespace", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ModelNamespace", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ContextType", "returnType": "System.Type", "parameterTypes": [], "isStatic": false}, {"name": "set_ContextType", "returnType": "System.Void", "parameterTypes": [["value", "System.Type"]], "isStatic": false}, {"name": "get_Language", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Language", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_UseNullableReferenceTypes", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_UseNullableReferenceTypes", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.ICompiledModelCodeGenerator", "methods": [{"name": "GenerateModel", "returnType": "System.Collections.Generic.IReadOnlyCollection`1<Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedFile>", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["options", "Microsoft.EntityFrameworkCore.Scaffolding.CompiledModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.ICompiledModelCodeGeneratorSelector", "methods": [{"name": "Select", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.ICompiledModelCodeGenerator", "parameterTypes": [["options", "Microsoft.EntityFrameworkCore.Scaffolding.CompiledModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.ICompiledModelScaffolder", "methods": [{"name": "ScaffoldModel", "returnType": "System.Collections.Generic.IReadOnlyList`1<System.String>", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["outputDir", "System.String"], ["options", "Microsoft.EntityFrameworkCore.Scaffolding.CompiledModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.IModelCodeGenerator", "methods": [{"name": "GenerateModel", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedModel", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["options", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.IModelCodeGeneratorSelector", "methods": [{"name": "Select", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.IModelCodeGenerator", "parameterTypes": [["language", "System.String"]], "isStatic": false}, {"name": "Select", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.IModelCodeGenerator", "parameterTypes": [["options", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.IReverseEngineerScaffolder", "methods": [{"name": "ScaffoldModel", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedModel", "parameterTypes": [["connectionString", "System.String"], ["databaseOptions", "Microsoft.EntityFrameworkCore.Scaffolding.DatabaseModelFactoryOptions"], ["modelOptions", "Microsoft.EntityFrameworkCore.Scaffolding.ModelReverseEngineerOptions"], ["codeOptions", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGenerationOptions"]], "isStatic": false}, {"name": "Save", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.SavedModelFiles", "parameterTypes": [["scaffoldedModel", "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedModel"], ["outputDir", "System.String"], ["overwriteFiles", "System.Boolean"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.IScaffoldingModelFactory", "methods": [{"name": "Create", "returnType": "Microsoft.EntityFrameworkCore.Metadata.IModel", "parameterTypes": [["databaseModel", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseModel"], ["options", "Microsoft.EntityFrameworkCore.Scaffolding.ModelReverseEngineerOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGenerationOptions", "methods": [{"name": "get_UseDataAnnotations", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_UseDataAnnotations", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_SuppressConnectionStringWarning", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_SuppressConnectionStringWarning", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_SuppressOnConfiguring", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_SuppressOnConfiguring", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_RootNamespace", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_RootNamespace", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ModelNamespace", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ModelNamespace", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ContextNamespace", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ContextNamespace", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Language", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Language", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_UseNullableReferenceTypes", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_UseNullableReferenceTypes", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_ContextDir", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ContextDir", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ContextName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ContextName", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ConnectionString", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ConnectionString", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ProjectDir", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ProjectDir", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGenerator", "methods": [{"name": "get_Language", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "GenerateModel", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedModel", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["options", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGeneratorDependencies", "methods": [{"name": "ToString", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "op_Inequality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGeneratorDependencies"], ["right", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGeneratorDependencies"]], "isStatic": true}, {"name": "op_Equality", "returnType": "System.Boolean", "parameterTypes": [["left", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGeneratorDependencies"], ["right", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGeneratorDependencies"]], "isStatic": true}, {"name": "GetHashCode", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["obj", "System.Object"]], "isStatic": false}, {"name": "Equals", "returnType": "System.Boolean", "parameterTypes": [["other", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGeneratorDependencies"]], "isStatic": false}, {"name": "<Clone>$", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGeneratorDependencies", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.ModelReverseEngineerOptions", "methods": [{"name": "get_UseDatabaseNames", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_UseDatabaseNames", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_NoPluralize", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_NoPluralize", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.SavedModelFiles", "methods": [{"name": "get_ContextFile", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_AdditionalFiles", "returnType": "System.Collections.Generic.IList`1<System.String>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedFile", "methods": [{"name": "get_Path", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Path", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Code", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Code", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedModel", "methods": [{"name": "get_ContextFile", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedFile", "parameterTypes": [], "isStatic": false}, {"name": "set_ContextFile", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedFile"]], "isStatic": false}, {"name": "get_AdditionalFiles", "returnType": "System.Collections.Generic.IList`1<Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedFile>", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.TemplatedModelGenerator", "methods": [{"name": "get_Language", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "HasTemplates", "returnType": "System.Boolean", "parameterTypes": [["projectDir", "System.String"]], "isStatic": false}], "fields": []}], "Microsoft.EntityFrameworkCore.Scaffolding.Internal": [{"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CandidateNamingService", "methods": [{"name": "GenerateCandidateIdentifier", "returnType": "System.String", "parameterTypes": [["originalTable", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseTable"]], "isStatic": false}, {"name": "GenerateCandidateIdentifier", "returnType": "System.String", "parameterTypes": [["originalColumn", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseColumn"]], "isStatic": false}, {"name": "GetDependentEndCandidateNavigationPropertyName", "returnType": "System.String", "parameterTypes": [["foreignKey", "Microsoft.EntityFrameworkCore.Metadata.IReadOnlyForeignKey"]], "isStatic": false}, {"name": "GetPrincipalEndCandidateNavigationPropertyName", "returnType": "System.String", "parameterTypes": [["foreignKey", "Microsoft.EntityFrameworkCore.Metadata.IReadOnlyForeignKey"], ["dependentEndNavigationPropertyName", "System.String"]], "isStatic": false}, {"name": "GenerateCandidateIdentifier", "returnType": "System.String", "parameterTypes": [["originalIdentifier", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CompiledModelCodeGeneratorSelector", "methods": [{"name": "Select", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.ICompiledModelCodeGenerator", "parameterTypes": [["options", "Microsoft.EntityFrameworkCore.Scaffolding.CompiledModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CompiledModelScaffolder", "methods": [{"name": "ScaffoldModel", "returnType": "System.Collections.Generic.IReadOnlyList`1<System.String>", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["outputDir", "System.String"], ["options", "Microsoft.EntityFrameworkCore.Scaffolding.CompiledModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpDbContextGenerator", "methods": [{"name": "TransformText", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Host", "returnType": "Microsoft.VisualStudio.TextTemplating.ITextTemplatingEngineHost", "parameterTypes": [], "isStatic": false}, {"name": "set_Host", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.VisualStudio.TextTemplating.ITextTemplatingEngineHost"]], "isStatic": false}, {"name": "Initialize", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpDbContextGeneratorBase", "methods": [{"name": "get_GenerationEnvironment", "returnType": "System.Text.StringBuilder", "parameterTypes": [], "isStatic": false}, {"name": "set_GenerationEnvironment", "returnType": "System.Void", "parameterTypes": [["value", "System.Text.StringBuilder"]], "isStatic": false}, {"name": "get_Errors", "returnType": "System.CodeDom.Compiler.CompilerErrorCollection", "parameterTypes": [], "isStatic": false}, {"name": "get_CurrentIndent", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Session", "returnType": "System.Collections.Generic.IDictionary`2<System.String,System.Object>", "parameterTypes": [], "isStatic": false}, {"name": "set_Session", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IDictionary`2<System.String,System.Object>"]], "isStatic": false}, {"name": "Write", "returnType": "System.Void", "parameterTypes": [["textToAppend", "System.String"]], "isStatic": false}, {"name": "WriteLine", "returnType": "System.Void", "parameterTypes": [["textToAppend", "System.String"]], "isStatic": false}, {"name": "Write", "returnType": "System.Void", "parameterTypes": [["format", "System.String"], ["args", "System.Object[]"]], "isStatic": false}, {"name": "WriteLine", "returnType": "System.Void", "parameterTypes": [["format", "System.String"], ["args", "System.Object[]"]], "isStatic": false}, {"name": "Error", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "Warning", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "<PERSON>ushInden<PERSON>", "returnType": "System.Void", "parameterTypes": [["indent", "System.String"]], "isStatic": false}, {"name": "PopIndent", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "ClearIndent", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "get_ToStringHelper", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpDbContextGeneratorBase/ToStringInstanceHelper", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpEntityTypeGenerator", "methods": [{"name": "TransformText", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Host", "returnType": "Microsoft.VisualStudio.TextTemplating.ITextTemplatingEngineHost", "parameterTypes": [], "isStatic": false}, {"name": "set_Host", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.VisualStudio.TextTemplating.ITextTemplatingEngineHost"]], "isStatic": false}, {"name": "Initialize", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpEntityTypeGeneratorBase", "methods": [{"name": "get_Errors", "returnType": "System.CodeDom.Compiler.CompilerErrorCollection", "parameterTypes": [], "isStatic": false}, {"name": "get_CurrentIndent", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Session", "returnType": "System.Collections.Generic.IDictionary`2<System.String,System.Object>", "parameterTypes": [], "isStatic": false}, {"name": "set_Session", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IDictionary`2<System.String,System.Object>"]], "isStatic": false}, {"name": "Write", "returnType": "System.Void", "parameterTypes": [["textToAppend", "System.String"]], "isStatic": false}, {"name": "WriteLine", "returnType": "System.Void", "parameterTypes": [["textToAppend", "System.String"]], "isStatic": false}, {"name": "Write", "returnType": "System.Void", "parameterTypes": [["format", "System.String"], ["args", "System.Object[]"]], "isStatic": false}, {"name": "WriteLine", "returnType": "System.Void", "parameterTypes": [["format", "System.String"], ["args", "System.Object[]"]], "isStatic": false}, {"name": "Error", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "Warning", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "<PERSON>ushInden<PERSON>", "returnType": "System.Void", "parameterTypes": [["indent", "System.String"]], "isStatic": false}, {"name": "PopIndent", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "ClearIndent", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "get_ToStringHelper", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpEntityTypeGeneratorBase/ToStringInstanceHelper", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpModelGenerator", "methods": [{"name": "get_Language", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "GenerateModel", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedModel", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["options", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpNamer`1", "methods": [{"name": "GetName", "returnType": "System.String", "parameterTypes": [["item", "T"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpRuntimeModelCodeGenerator", "methods": [{"name": "get_Language", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "GenerateModel", "returnType": "System.Collections.Generic.IReadOnlyCollection`1<Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedFile>", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["options", "Microsoft.EntityFrameworkCore.Scaffolding.CompiledModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpUniqueNamer`1", "methods": [{"name": "GetName", "returnType": "System.String", "parameterTypes": [["item", "T"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.CSharpUtilities", "methods": [{"name": "IsCSharpKeyword", "returnType": "System.Boolean", "parameterTypes": [["identifier", "System.String"]], "isStatic": false}, {"name": "GenerateCSharpIdentifier", "returnType": "System.String", "parameterTypes": [["identifier", "System.String"], ["existingIdentifiers", "System.Collections.Generic.ICollection`1<System.String>"], ["singularizePluralizer", "System.Func`2<System.String,System.String>"]], "isStatic": false}, {"name": "GenerateCSharpIdentifier", "returnType": "System.String", "parameterTypes": [["identifier", "System.String"], ["existingIdentifiers", "System.Collections.Generic.ICollection`1<System.String>"], ["singularizePluralizer", "System.Func`2<System.String,System.String>"], ["uniquifier", "System.Func`3<System.String,System.Collections.Generic.ICollection`1<System.String>,System.String>"]], "isStatic": false}, {"name": "Uniquifier", "returnType": "System.String", "parameterTypes": [["proposedIdentifier", "System.String"], ["existingIdentifiers", "System.Collections.Generic.ICollection`1<System.String>"]], "isStatic": false}, {"name": "IsValidIdentifier", "returnType": "System.Boolean", "parameterTypes": [["name", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.ICandidateNamingService", "methods": [{"name": "GenerateCandidateIdentifier", "returnType": "System.String", "parameterTypes": [["originalTable", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseTable"]], "isStatic": false}, {"name": "GenerateCandidateIdentifier", "returnType": "System.String", "parameterTypes": [["originalColumn", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseColumn"]], "isStatic": false}, {"name": "GenerateCandidateIdentifier", "returnType": "System.String", "parameterTypes": [["databaseName", "System.String"]], "isStatic": false}, {"name": "GetDependentEndCandidateNavigationPropertyName", "returnType": "System.String", "parameterTypes": [["foreignKey", "Microsoft.EntityFrameworkCore.Metadata.IReadOnlyForeignKey"]], "isStatic": false}, {"name": "GetPrincipalEndCandidateNavigationPropertyName", "returnType": "System.String", "parameterTypes": [["foreignKey", "Microsoft.EntityFrameworkCore.Metadata.IReadOnlyForeignKey"], ["dependentEndNavigationPropertyName", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.ICSharpUtilities", "methods": [{"name": "GenerateCSharpIdentifier", "returnType": "System.String", "parameterTypes": [["identifier", "System.String"], ["existingIdentifiers", "System.Collections.Generic.ICollection`1<System.String>"], ["singularizePluralizer", "System.Func`2<System.String,System.String>"]], "isStatic": false}, {"name": "GenerateCSharpIdentifier", "returnType": "System.String", "parameterTypes": [["identifier", "System.String"], ["existingIdentifiers", "System.Collections.Generic.ICollection`1<System.String>"], ["singularizePluralizer", "System.Func`2<System.String,System.String>"], ["uniquifier", "System.Func`3<System.String,System.Collections.Generic.ICollection`1<System.String>,System.String>"]], "isStatic": false}, {"name": "IsCSharpKeyword", "returnType": "System.Boolean", "parameterTypes": [["identifier", "System.String"]], "isStatic": false}, {"name": "IsValidIdentifier", "returnType": "System.Boolean", "parameterTypes": [["name", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.IScaffoldingTypeMapper", "methods": [{"name": "FindMapping", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.TypeScaffoldingInfo", "parameterTypes": [["storeType", "System.String"], ["keyOrIndex", "System.Boolean"], ["rowVersion", "System.Boolean"], ["clrType", "System.Type"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.ITextTransformation", "methods": [{"name": "get_Session", "returnType": "System.Collections.Generic.IDictionary`2<System.String,System.Object>", "parameterTypes": [], "isStatic": false}, {"name": "set_Session", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IDictionary`2<System.String,System.Object>"]], "isStatic": false}, {"name": "get_Errors", "returnType": "System.CodeDom.Compiler.CompilerErrorCollection", "parameterTypes": [], "isStatic": false}, {"name": "Initialize", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "TransformText", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.ModelCodeGeneratorSelector", "methods": [{"name": "Select", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.IModelCodeGenerator", "parameterTypes": [["options", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.RelationalScaffoldingModelFactory", "methods": [{"name": "Create", "returnType": "Microsoft.EntityFrameworkCore.Metadata.IModel", "parameterTypes": [["databaseModel", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseModel"], ["options", "Microsoft.EntityFrameworkCore.Scaffolding.ModelReverseEngineerOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.ReverseEngineerScaffolder", "methods": [{"name": "ScaffoldModel", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedModel", "parameterTypes": [["connectionString", "System.String"], ["databaseOptions", "Microsoft.EntityFrameworkCore.Scaffolding.DatabaseModelFactoryOptions"], ["modelOptions", "Microsoft.EntityFrameworkCore.Scaffolding.ModelReverseEngineerOptions"], ["codeOptions", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGenerationOptions"]], "isStatic": false}, {"name": "Save", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.SavedModelFiles", "parameterTypes": [["scaffoldedModel", "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedModel"], ["outputDir", "System.String"], ["overwriteFiles", "System.Boolean"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.ScaffoldingTypeMapper", "methods": [{"name": "FindMapping", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.TypeScaffoldingInfo", "parameterTypes": [["storeType", "System.String"], ["keyOrIndex", "System.Boolean"], ["rowVersion", "System.Boolean"], ["clrType", "System.Type"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.TextTemplatingEngineHost", "methods": [{"name": "get_Session", "returnType": "Microsoft.VisualStudio.TextTemplating.ITextTemplatingSession", "parameterTypes": [], "isStatic": false}, {"name": "set_Session", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.VisualStudio.TextTemplating.ITextTemplatingSession"]], "isStatic": false}, {"name": "get_StandardAssemblyReferences", "returnType": "System.Collections.Generic.IList`1<System.String>", "parameterTypes": [], "isStatic": false}, {"name": "get_StandardImports", "returnType": "System.Collections.Generic.IList`1<System.String>", "parameterTypes": [], "isStatic": false}, {"name": "get_TemplateFile", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_TemplateFile", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Extension", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_Errors", "returnType": "System.CodeDom.Compiler.CompilerErrorCollection", "parameterTypes": [], "isStatic": false}, {"name": "get_OutputEncoding", "returnType": "System.Text.Encoding", "parameterTypes": [], "isStatic": false}, {"name": "Initialize", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}, {"name": "CreateSession", "returnType": "Microsoft.VisualStudio.TextTemplating.ITextTemplatingSession", "parameterTypes": [], "isStatic": false}, {"name": "GetHostOption", "returnType": "System.Object", "parameterTypes": [["optionName", "System.String"]], "isStatic": false}, {"name": "LoadIncludeText", "returnType": "System.Boolean", "parameterTypes": [["requestFileName", "System.String"], ["content", "System.String&"], ["location", "System.String&"]], "isStatic": false}, {"name": "LogErrors", "returnType": "System.Void", "parameterTypes": [["errors", "System.CodeDom.Compiler.CompilerErrorCollection"]], "isStatic": false}, {"name": "ProvideTemplatingAppDomain", "returnType": "System.AppDomain", "parameterTypes": [["content", "System.String"]], "isStatic": false}, {"name": "ResolveAssemblyReference", "returnType": "System.String", "parameterTypes": [["assemblyReference", "System.String"]], "isStatic": false}, {"name": "ResolveDirectiveProcessor", "returnType": "System.Type", "parameterTypes": [["processorName", "System.String"]], "isStatic": false}, {"name": "ResolveParameterValue", "returnType": "System.String", "parameterTypes": [["directiveId", "System.String"], ["processorName", "System.String"], ["parameterName", "System.String"]], "isStatic": false}, {"name": "ResolvePath", "returnType": "System.String", "parameterTypes": [["path", "System.String"]], "isStatic": false}, {"name": "SetFileExtension", "returnType": "System.Void", "parameterTypes": [["extension", "System.String"]], "isStatic": false}, {"name": "SetOutputEncoding", "returnType": "System.Void", "parameterTypes": [["encoding", "System.Text.Encoding"], ["fromOutputDirective", "System.Boolean"]], "isStatic": false}, {"name": "GetService", "returnType": "System.Object", "parameterTypes": [["serviceType", "System.Type"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.TextTemplatingModelGenerator", "methods": [{"name": "HasTemplates", "returnType": "System.Boolean", "parameterTypes": [["projectDir", "System.String"]], "isStatic": false}, {"name": "GenerateModel", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.ScaffoldedModel", "parameterTypes": [["model", "Microsoft.EntityFrameworkCore.Metadata.IModel"], ["options", "Microsoft.EntityFrameworkCore.Scaffolding.ModelCodeGenerationOptions"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Internal.TypeScaffoldingInfo", "methods": [{"name": "get_ClrType", "returnType": "System.Type", "parameterTypes": [], "isStatic": false}, {"name": "get_IsInferred", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "get_ScaffoldUnicode", "returnType": "System.Nullable`1<System.Boolean>", "parameterTypes": [], "isStatic": false}, {"name": "get_ScaffoldFixedLength", "returnType": "System.Nullable`1<System.Boolean>", "parameterTypes": [], "isStatic": false}, {"name": "get_ScaffoldMaxLength", "returnType": "System.Nullable`1<System.Int32>", "parameterTypes": [], "isStatic": false}, {"name": "get_ScaffoldPrecision", "returnType": "System.Nullable`1<System.Int32>", "parameterTypes": [], "isStatic": false}, {"name": "get_ScaffoldScale", "returnType": "System.Nullable`1<System.Int32>", "parameterTypes": [], "isStatic": false}], "fields": []}], "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.Internal": [{"name": "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.Internal.DatabaseColumnExtensions", "methods": [{"name": "DisplayName", "returnType": "System.String", "parameterTypes": [["column", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseColumn"]], "isStatic": true}, {"name": "IsKeyOrIndex", "returnType": "System.Boolean", "parameterTypes": [["column", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseColumn"]], "isStatic": true}, {"name": "IsRowVersion", "returnType": "System.Boolean", "parameterTypes": [["column", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseColumn"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.Internal.DatabaseForeignKeyExtensions", "methods": [{"name": "DisplayName", "returnType": "System.String", "parameterTypes": [["foreignKey", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseForeignKey"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.Internal.DatabaseTableExtensions", "methods": [{"name": "DisplayName", "returnType": "System.String", "parameterTypes": [["table", "Microsoft.EntityFrameworkCore.Scaffolding.Metadata.DatabaseTable"]], "isStatic": true}], "fields": []}], "Microsoft.EntityFrameworkCore.Design": [{"name": "Microsoft.EntityFrameworkCore.Design.DbContextActivator", "methods": [{"name": "CreateInstance", "returnType": "Microsoft.EntityFrameworkCore.DbContext", "parameterTypes": [["contextType", "System.Type"], ["startupAssembly", "System.Reflection.Assembly"], ["reportHandler", "Microsoft.EntityFrameworkCore.Design.IOperationReportHandler"]], "isStatic": true}, {"name": "CreateInstance", "returnType": "Microsoft.EntityFrameworkCore.DbContext", "parameterTypes": [["contextType", "System.Type"], ["startupAssembly", "System.Reflection.Assembly"], ["reportHandler", "Microsoft.EntityFrameworkCore.Design.IOperationReportHandler"], ["args", "System.String[]"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.DesignTimeServiceCollectionExtensions", "methods": [{"name": "AddEntityFrameworkDesignTimeServices", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceCollection", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["reporter", "Microsoft.EntityFrameworkCore.Design.Internal.IOperationReporter"], ["applicationServiceProviderAccessor", "System.Func`1<System.IServiceProvider>"]], "isStatic": true}, {"name": "AddDbContextDesignTimeServices", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceCollection", "parameterTypes": [["services", "Microsoft.Extensions.DependencyInjection.IServiceCollection"], ["context", "Microsoft.EntityFrameworkCore.DbContext"]], "isStatic": true}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "methods": [{"name": "get_Namespace", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Namespace", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_DeclaringType", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_DeclaringType", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Method", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Method", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_TypeArguments", "returnType": "System.Collections.Generic.IList`1<System.String>", "parameterTypes": [], "isStatic": false}, {"name": "set_TypeArguments", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IList`1<System.String>"]], "isStatic": false}, {"name": "get_Arguments", "returnType": "System.Collections.Generic.IList`1<System.Object>", "parameterTypes": [], "isStatic": false}, {"name": "set_Arguments", "returnType": "System.Void", "parameterTypes": [["value", "System.Collections.Generic.IList`1<System.Object>"]], "isStatic": false}, {"name": "get_IsHandledByDataAnnotations", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "set_IsHandledByDataAnnotations", "returnType": "System.Void", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "get_ChainedCall", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [], "isStatic": false}, {"name": "set_ChainedCall", "returnType": "System.Void", "parameterTypes": [["value", "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment"]], "isStatic": false}, {"name": "From", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [["call", "Microsoft.EntityFrameworkCore.Design.MethodCallCodeFragment"]], "isStatic": true}, {"name": "Chain", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [["call", "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment"]], "isStatic": false}, {"name": "GetRequiredUsings", "returnType": "System.Collections.Generic.IEnumerable`1<System.String>", "parameterTypes": [], "isStatic": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment", "parameterTypes": [["predicate", "System.Func`2<Microsoft.EntityFrameworkCore.Design.FluentApiCodeFragment,System.Boolean>"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.ILanguageBasedService", "methods": [{"name": "get_Language", "returnType": "System.String", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.IOperationReportHandler", "methods": [{"name": "get_Version", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "OnError", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "OnWarning", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "OnInformation", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "OnVerbose", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.IOperationResultHandler", "methods": [{"name": "get_Version", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "OnResult", "returnType": "System.Void", "parameterTypes": [["value", "System.Object"]], "isStatic": false}, {"name": "OnError", "returnType": "System.Void", "parameterTypes": [["type", "System.String"], ["message", "System.String"], ["stackTrace", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.IPluralizer", "methods": [{"name": "Pluralize", "returnType": "System.String", "parameterTypes": [["identifier", "System.String"]], "isStatic": false}, {"name": "Singularize", "returnType": "System.String", "parameterTypes": [["identifier", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.NamespaceComparer", "methods": [{"name": "Compare", "returnType": "System.Int32", "parameterTypes": [["x", "System.String"], ["y", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.OperationException", "methods": [], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.OperationExecutor", "methods": [], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.OperationReportHandler", "methods": [{"name": "get_Version", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "OnError", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "OnWarning", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "OnInformation", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "OnVerbose", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.OperationResultHandler", "methods": [{"name": "get_Version", "returnType": "System.Int32", "parameterTypes": [], "isStatic": false}, {"name": "get_<PERSON><PERSON><PERSON>ult", "returnType": "System.Boolean", "parameterTypes": [], "isStatic": false}, {"name": "get_Result", "returnType": "System.Object", "parameterTypes": [], "isStatic": false}, {"name": "get_ErrorType", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_ErrorMessage", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "get_ErrorStackTrace", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "OnResult", "returnType": "System.Void", "parameterTypes": [["value", "System.Object"]], "isStatic": false}, {"name": "OnError", "returnType": "System.Void", "parameterTypes": [["type", "System.String"], ["message", "System.String"], ["stackTrace", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.MethodCallCodeFragmentExtensions", "methods": [{"name": "GetRequiredUsings", "returnType": "System.Collections.Generic.IEnumerable`1<System.String>", "parameterTypes": [["methodCall", "Microsoft.EntityFrameworkCore.Design.MethodCallCodeFragment"]], "isStatic": true}], "fields": []}], "Microsoft.EntityFrameworkCore.Design.Internal": [{"name": "Microsoft.EntityFrameworkCore.Design.Internal.AppServiceProviderFactory", "methods": [{"name": "Create", "returnType": "System.IServiceProvider", "parameterTypes": [["args", "System.String[]"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.ContextInfo", "methods": [{"name": "get_Type", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Type", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_ProviderName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_ProviderName", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_DatabaseName", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_DatabaseName", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_DataSource", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_DataSource", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Options", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Options", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.CSharpHelper", "methods": [{"name": "Lambda", "returnType": "System.String", "parameterTypes": [["properties", "System.Collections.Generic.IReadOnlyList`1<System.String>"], ["lambdaIdentifier", "System.String"]], "isStatic": false}, {"name": "Reference", "returnType": "System.String", "parameterTypes": [["type", "System.Type"], ["fullName", "System.Nullable`1<System.Boolean>"]], "isStatic": false}, {"name": "Identifier", "returnType": "System.String", "parameterTypes": [["name", "System.String"], ["scope", "System.Collections.Generic.ICollection`1<System.String>"], ["capitalize", "System.Nullable`1<System.Boolean>"]], "isStatic": false}, {"name": "Namespace", "returnType": "System.String", "parameterTypes": [["name", "System.String[]"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Boolean"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Byte"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Char"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.DateOnly"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.DateTime"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.DateTimeOffset"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Decimal"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Double"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Single"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Guid"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Int32"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Int64"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.SByte"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Int16"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.TimeOnly"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.TimeSpan"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.UInt32"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.UInt64"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.UInt16"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Numerics.BigInteger"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Type"], ["useFullName", "System.Nullable`1<System.Boolean>"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Nullable`1<T>"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["values", "T[]"], ["vertical", "System.Boolean"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["values", "System.Object[0...,0...]"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["values", "System.Collections.Generic.List`1<T>"], ["vertical", "System.Boolean"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["dict", "System.Collections.Generic.Dictionary`2<<PERSON><PERSON><PERSON>,TValue>"], ["vertical", "System.Boolean"]], "isStatic": false}, {"name": "Literal", "returnType": "System.String", "parameterTypes": [["value", "System.Enum"], ["fullName", "System.Boolean"]], "isStatic": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "returnType": "System.String", "parameterTypes": [["value", "System.Object"]], "isStatic": false}, {"name": "Fragment", "returnType": "System.String", "parameterTypes": [["fragment", "Microsoft.EntityFrameworkCore.Design.IMethodCallCodeFragment"], ["instanceIdentifier", "System.String"], ["typeQualified", "System.Boolean"]], "isStatic": false}, {"name": "Fragment", "returnType": "System.String", "parameterTypes": [["fragment", "Microsoft.EntityFrameworkCore.Design.IMethodCallCodeFragment"], ["indent", "System.Int32"]], "isStatic": false}, {"name": "Fragment", "returnType": "System.String", "parameterTypes": [["fragment", "Microsoft.EntityFrameworkCore.Design.NestedClosureCodeFragment"], ["indent", "System.Int32"]], "isStatic": false}, {"name": "Fragment", "returnType": "System.String", "parameterTypes": [["fragment", "Microsoft.EntityFrameworkCore.Design.PropertyAccessorCodeFragment"]], "isStatic": false}, {"name": "Fragment", "returnType": "System.String", "parameterTypes": [["fragment", "Microsoft.EntityFrameworkCore.Design.AttributeCodeFragment"]], "isStatic": false}, {"name": "XmlComment", "returnType": "System.String", "parameterTypes": [["comment", "System.String"], ["indent", "System.Int32"]], "isStatic": false}, {"name": "Arguments", "returnType": "System.String", "parameterTypes": [["values", "System.Collections.Generic.IEnumerable`1<System.Object>"]], "isStatic": false}, {"name": "GetRequiredUsings", "returnType": "System.Collections.Generic.IEnumerable`1<System.String>", "parameterTypes": [["type", "System.Type"]], "isStatic": false}, {"name": "Statement", "returnType": "System.String", "parameterTypes": [["node", "System.Linq.Expressions.Expression"], ["collectedNamespaces", "System.Collections.Generic.ISet`1<System.String>"]], "isStatic": false}, {"name": "Expression", "returnType": "System.String", "parameterTypes": [["node", "System.Linq.Expressions.Expression"], ["collectedNamespaces", "System.Collections.Generic.ISet`1<System.String>"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.DatabaseOperations", "methods": [{"name": "ScaffoldContext", "returnType": "Microsoft.EntityFrameworkCore.Scaffolding.SavedModelFiles", "parameterTypes": [["provider", "System.String"], ["connectionString", "System.String"], ["outputDir", "System.String"], ["outputContextDir", "System.String"], ["dbContextClassName", "System.String"], ["schemas", "System.Collections.Generic.IEnumerable`1<System.String>"], ["tables", "System.Collections.Generic.IEnumerable`1<System.String>"], ["modelNamespace", "System.String"], ["contextNamespace", "System.String"], ["useDataAnnotations", "System.Boolean"], ["overwriteFiles", "System.Boolean"], ["useDatabaseNames", "System.Boolean"], ["suppressOnConfiguring", "System.Boolean"], ["noPluralize", "System.Boolean"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.DbContextOperations", "methods": [{"name": "DropDatabase", "returnType": "System.Void", "parameterTypes": [["contextType", "System.String"]], "isStatic": false}, {"name": "ScriptDbContext", "returnType": "System.String", "parameterTypes": [["contextType", "System.String"]], "isStatic": false}, {"name": "Optimize", "returnType": "System.Void", "parameterTypes": [["outputDir", "System.String"], ["modelNamespace", "System.String"], ["contextTypeName", "System.String"]], "isStatic": false}, {"name": "CreateContext", "returnType": "Microsoft.EntityFrameworkCore.DbContext", "parameterTypes": [["contextType", "System.String"]], "isStatic": false}, {"name": "GetContextTypes", "returnType": "System.Collections.Generic.IEnumerable`1<System.Type>", "parameterTypes": [], "isStatic": false}, {"name": "GetContextType", "returnType": "System.Type", "parameterTypes": [["name", "System.String"]], "isStatic": false}, {"name": "GetContextInfo", "returnType": "Microsoft.EntityFrameworkCore.Design.Internal.ContextInfo", "parameterTypes": [["contextType", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.DesignTimeConnectionStringResolver", "methods": [], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.DesignTimeServicesBuilder", "methods": [{"name": "Build", "returnType": "System.IServiceProvider", "parameterTypes": [["context", "Microsoft.EntityFrameworkCore.DbContext"]], "isStatic": false}, {"name": "CreateServiceCollection", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceCollection", "parameterTypes": [["context", "Microsoft.EntityFrameworkCore.DbContext"]], "isStatic": false}, {"name": "Build", "returnType": "System.IServiceProvider", "parameterTypes": [["provider", "System.String"]], "isStatic": false}, {"name": "CreateServiceCollection", "returnType": "Microsoft.Extensions.DependencyInjection.IServiceCollection", "parameterTypes": [["provider", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.HumanizerPluralizer", "methods": [{"name": "Pluralize", "returnType": "System.String", "parameterTypes": [["name", "System.String"]], "isStatic": false}, {"name": "Singularize", "returnType": "System.String", "parameterTypes": [["name", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.IDesignTimeConnectionStringResolver", "methods": [], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.IOperationReporter", "methods": [{"name": "WriteError", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "WriteWarning", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "WriteInformation", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "WriteVerbose", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "Write", "returnType": "System.Void", "parameterTypes": [["error", "System.CodeDom.Compiler.CompilerError"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.LanguageBasedSelector`1", "methods": [{"name": "Select", "returnType": "T", "parameterTypes": [["language", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.MigrationInfo", "methods": [{"name": "get_Id", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Id", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Name", "returnType": "System.String", "parameterTypes": [], "isStatic": false}, {"name": "set_Name", "returnType": "System.Void", "parameterTypes": [["value", "System.String"]], "isStatic": false}, {"name": "get_Applied", "returnType": "System.Nullable`1<System.Boolean>", "parameterTypes": [], "isStatic": false}, {"name": "set_Applied", "returnType": "System.Void", "parameterTypes": [["value", "System.Nullable`1<System.Boolean>"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.MigrationsOperations", "methods": [{"name": "AddMigration", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationFiles", "parameterTypes": [["name", "System.String"], ["outputDir", "System.String"], ["contextType", "System.String"], ["namespace", "System.String"]], "isStatic": false}, {"name": "GetMigrations", "returnType": "System.Collections.Generic.IEnumerable`1<Microsoft.EntityFrameworkCore.Design.Internal.MigrationInfo>", "parameterTypes": [["contextType", "System.String"], ["connectionString", "System.String"], ["noConnect", "System.Boolean"]], "isStatic": false}, {"name": "ScriptMigration", "returnType": "System.String", "parameterTypes": [["fromMigration", "System.String"], ["toMigration", "System.String"], ["options", "Microsoft.EntityFrameworkCore.Migrations.MigrationsSqlGenerationOptions"], ["contextType", "System.String"]], "isStatic": false}, {"name": "UpdateDatabase", "returnType": "System.Void", "parameterTypes": [["targetMigration", "System.String"], ["connectionString", "System.String"], ["contextType", "System.String"]], "isStatic": false}, {"name": "RemoveMigration", "returnType": "Microsoft.EntityFrameworkCore.Migrations.Design.MigrationFiles", "parameterTypes": [["contextType", "System.String"], ["force", "System.Boolean"]], "isStatic": false}, {"name": "HasPendingModelChanges", "returnType": "System.Void", "parameterTypes": [["contextType", "System.String"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.OperationLogger", "methods": [{"name": "IsEnabled", "returnType": "System.Boolean", "parameterTypes": [["logLevel", "Microsoft.Extensions.Logging.LogLevel"]], "isStatic": false}, {"name": "BeginScope", "returnType": "System.IDisposable", "parameterTypes": [["state", "TState"]], "isStatic": false}, {"name": "Log", "returnType": "System.Void", "parameterTypes": [["logLevel", "Microsoft.Extensions.Logging.LogLevel"], ["eventId", "Microsoft.Extensions.Logging.EventId"], ["state", "TState"], ["exception", "System.Exception"], ["formatter", "System.Func`3<TState,System.Exception,System.String>"]], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.OperationLoggerProvider", "methods": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "returnType": "Microsoft.Extensions.Logging.ILogger", "parameterTypes": [["categoryName", "System.String"]], "isStatic": false}, {"name": "Dispose", "returnType": "System.Void", "parameterTypes": [], "isStatic": false}], "fields": []}, {"name": "Microsoft.EntityFrameworkCore.Design.Internal.OperationReporter", "methods": [{"name": "WriteError", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "WriteWarning", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "WriteInformation", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}, {"name": "WriteVerbose", "returnType": "System.Void", "parameterTypes": [["message", "System.String"]], "isStatic": false}], "fields": []}], "Microsoft.Extensions.Hosting": [{"name": "Microsoft.Extensions.Hosting.HostFactoryResolver", "methods": [{"name": "ResolveWebHostFactory", "returnType": "System.Func`2<System.String[],TWebHost>", "parameterTypes": [["assembly", "System.Reflection.Assembly"]], "isStatic": true}, {"name": "ResolveWebHostBuilderFactory", "returnType": "System.Func`2<System.String[],TWebHostBuilder>", "parameterTypes": [["assembly", "System.Reflection.Assembly"]], "isStatic": true}, {"name": "ResolveHostBuilderFactory", "returnType": "System.Func`2<System.String[],THostBuilder>", "parameterTypes": [["assembly", "System.Reflection.Assembly"]], "isStatic": true}, {"name": "ResolveHostFactory", "returnType": "System.Func`2<System.String[],System.Object>", "parameterTypes": [["assembly", "System.Reflection.Assembly"], ["waitTimeout", "System.Nullable`1<System.TimeSpan>"], ["stopApplication", "System.Boolean"], ["configureHostBuilder", "System.Action`1<System.Object>"], ["entrypointCompleted", "System.Action`1<System.Exception>"]], "isStatic": true}, {"name": "ResolveServiceProviderFactory", "returnType": "System.Func`2<System.String[],System.IServiceProvider>", "parameterTypes": [["assembly", "System.Reflection.Assembly"], ["waitTimeout", "System.Nullable`1<System.TimeSpan>"]], "isStatic": true}], "fields": []}]}