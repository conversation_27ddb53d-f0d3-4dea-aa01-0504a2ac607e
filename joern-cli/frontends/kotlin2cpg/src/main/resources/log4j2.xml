<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="info">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d{yyy-MM-dd HH:mm:ss.SSS} %highlight{%-5p} %msg%n" noConsoleNoAnsi="true" />
        </Console>
    </Appenders>
    <Loggers>
        <Logger name="io.grpc" level="error"/>
        <Logger name="io.netty" level="error"/>

        <Root level="trace">
            <AppenderRef ref="Console" level="${env:SL_LOGGING_LEVEL:-info}"/>
        </Root>
    </Loggers>
</Configuration>
