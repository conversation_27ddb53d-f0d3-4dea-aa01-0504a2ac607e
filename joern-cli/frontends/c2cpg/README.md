[![CI pipeline](https://github.com/ShiftLeftSecurity/codepropertygraph/actions/workflows/push.yml/badge.svg)](https://github.com/ShiftLeftSecurity/codepropertygraph/actions/workflows/push.yml)

# c2cpg

An [Eclipse CDT](https://wiki.eclipse.org/CDT/designs/Overview_of_Parsing) based parser for C/C++ that creates code property graphs according to the specification at https://github.com/ShiftLeftSecurity/codepropertygraph .

## Self-published cdt-core dependency
Eclipse cdt-core is unfortunately not published to maven central (instead they use their own repository format called p2 tycho, for which tooling is rather limited). Therefor we re-publish it to [maven central](https://repo1.maven.org/maven2/io/joern/eclipse-cdt-core/) under the `io.joern` organization, so we can use it in joern as well as other downstream projects. 
If you want to upgrade cdt-core, edit the eclipse-cdt/eclipse-cdt-core-publish.sh script and adapt the top two variables (`JAR_URL` and `CUSTOM_RELEASE_VERSION`), then run the script and follow the instructions.
Once it's on maven central, update the version in build.sbt and give it a try!


## Building the code

The build process has been verified on Linux, and it should be possible 
to build on OS X and BSD systems as well. The build process requires
the following prerequisites:

* Java runtime 11
  - Link: http://openjdk.java.net/install/
* Scala build tool (sbt)
  - Link: https://www.scala-sbt.org/

Additional build-time dependencies are automatically downloaded as part
of the build process. To build c2cpg issue the command `sbt stage`.

## Running

To produce a code property graph  issue the command:
```
./c2cpg.sh <path/to/sourceCodeDirectory> --output <path/to/outputCpg>
`````

Additional options are available:
```
./c2cpg.sh <path/to/sourceCodeDirectory> \
                --output <path/to/outputCpg> \
                --include <path/to/include/dir1>,<path/to/include/dir2>
                --define DEF
                --define DEF_VAL=2
```

## CLI Arguments

Run the following to see a complete list of available options:
```
./c2cpg.sh --help
```

```
Usage: C2Cpg [options] input-dir

  input-dir                source directory
  -o, --output <value>     output filename
  --exclude <file1>        files or folders to exclude during CPG generation (paths relative to <input-dir> or absolute paths)
  --exclude-regex <value>  a regex specifying files to exclude during CPG generation (paths relative to <input-dir> are matched)
  --enable-early-schema-checking
                           enables early schema validation during AST creation (disabled by default)
  --enable-file-content    add the raw source code to the content field of FILE nodes to allow for method source retrieval via offset fields (disabled by default)
  --help                   display this help message
  --include-comments       includes all comments into the CPG
  --log-problems           enables logging of all parse problems while generating the CPG
  --log-preprocessor       enables logging of all preprocessor statements while generating the CPG
  --print-ifdef-only       prints a comma-separated list of all preprocessor ifdef and if statements; does not create a CPG
  --include <value>        header include paths
  --with-include-auto-discovery
                           enables auto discovery of system header include paths
  --skip-function-bodies   instructs the parser to skip function and method bodies.
  --with-preprocessed-files
                           includes *.i files and gives them priority over their unprocessed origin source files.
  --define <value>         define a name
  --compilation-database <value>
                           enables the processing of compilation database files (e.g., compile_commands.json).
 This allows to automatically extract compiler options, source files, and other build information from the specified database
 and ensuring consistency with the build configuration.
 For a cmake based build such a file is generated with the environment variable CMAKE_EXPORT_COMPILE_COMMANDS being present.
 Clang based build are supported e.g., with https://github.com/rizsotto/Bear
```

## Dealing with Parser problems:
(copied from [here](https://wiki.eclipse.org/CDT/designs/Overview_of_Parsing))

In case a syntax error is encountered in the source a _problem node_ will be generated in the AST. There are four types of problem node:
 - `IASTProblemDeclaration`
 - `IASTProblemExpression`
 - `IASTProblemStatement`
 - `IASTProblemTypeId`

The parser is usually capable of recovering from most syntax errors, generating a problem node and resuming the parse.
Problem nodes may also be generated by the preprocessor, for example if a macro is not used properly or if an included file cannot be found.
