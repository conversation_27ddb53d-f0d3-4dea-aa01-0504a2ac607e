// Advanced Joern script for detailed data flow analysis from $_GET['cmd'] to system()
// This script provides comprehensive node properties, edge details, and DDG information

import io.joern.dataflowengineoss.language.*
import io.shiftleft.semanticcpg.language.*
import io.shiftleft.codepropertygraph.generated.EdgeTypes

@main def main(inputPath: String) = {
  // Import the PHP code for analysis
  importCode(inputPath)

  println("=" * 80)
  println("ADVANCED PHP COMMAND INJECTION ANALYSIS")
  println("=" * 80)

  // Define multiple source patterns for $_GET['cmd']
  def getAssignmentSources = cpg.assignment.source.code(".*_GET.*cmd.*")
  def getCallSources = cpg.call.code(".*_GET.*cmd.*")
  def getIdentifierSources = cpg.identifier.code(".*_GET.*cmd.*")
  def allGetSources = cpg.assignment.source.code(".*_GET.*")

  // Combine all source patterns
  def sources = getAssignmentSources ++ getCallSources ++ getIdentifierSources

  // Define sink: system() function calls and arguments
  def systemCalls = cpg.call.name("system")
  def systemArguments = systemCalls.argument

  println(s"\n📍 SOURCE ANALYSIS:")
  println(s"   Assignment sources: ${getAssignmentSources.size}")
  println(s"   Call sources: ${getCallSources.size}")
  println(s"   Identifier sources: ${getIdentifierSources.size}")
  println(s"   Total specific sources: ${sources.size}")
  println(s"   All \$_GET sources: ${allGetSources.size}")

  println(s"\n🎯 SINK ANALYSIS:")
  println(s"   System calls: ${systemCalls.size}")
  println(s"   System arguments: ${systemArguments.size}")

  // Detailed node property analysis
  def printNodeDetails(node: Any, prefix: String = "") = {
    node match {
      case n: io.shiftleft.codepropertygraph.generated.nodes.StoredNode =>
        println(s"${prefix}📋 Node Details:")
        println(s"${prefix}   ID: ${n.id}")
        println(s"${prefix}   Label: ${n.label}")
        println(s"${prefix}   Code: ${n.code}")
        println(s"${prefix}   Line: ${n.lineNumber.getOrElse("N/A")}")
        println(s"${prefix}   Column: ${n.columnNumber.getOrElse("N/A")}")
        println(s"${prefix}   Order: ${n.order}")
        println(s"${prefix}   File: ${n.file.name.headOption.getOrElse("N/A")}")

        // Additional properties based on node type
        n match {
          case call: io.shiftleft.codepropertygraph.generated.nodes.Call =>
            println(s"${prefix}   Call Name: ${call.name}")
            println(s"${prefix}   Method Full Name: ${call.methodFullName}")
            println(s"${prefix}   Argument Index: ${call.argumentIndex}")
            println(s"${prefix}   Type Full Name: ${call.typeFullName}")
          case identifier: io.shiftleft.codepropertygraph.generated.nodes.Identifier =>
            println(s"${prefix}   Name: ${identifier.name}")
            println(s"${prefix}   Type Full Name: ${identifier.typeFullName}")
          case assignment: io.shiftleft.codepropertygraph.generated.nodes.Call if assignment.name.contains("assignment") =>
            println(s"${prefix}   Assignment Type: ${assignment.name}")
          case _ =>
        }
      case _ =>
        println(s"${prefix}📋 Node: ${node.toString}")
    }
  }

  // Print detailed source information
  println(s"\n🔍 DETAILED SOURCE ANALYSIS:")
  sources.zipWithIndex.foreach { case (source, idx) =>
    println(s"\n--- Source ${idx + 1} ---")
    printNodeDetails(source, "   ")
  }

  // Print detailed sink information
  println(s"\n🔍 DETAILED SINK ANALYSIS:")
  systemCalls.zipWithIndex.foreach { case (sink, idx) =>
    println(s"\n--- System Call ${idx + 1} ---")
    printNodeDetails(sink, "   ")

    // Print arguments details
    sink.argument.zipWithIndex.foreach { case (arg, argIdx) =>
      println(s"\n   --- Argument ${argIdx + 1} ---")
      printNodeDetails(arg, "      ")
    }
  }

  // Data flow analysis
  println(s"\n🌊 DATA FLOW ANALYSIS:")
  val flows = systemArguments.reachableByFlows(sources)
  println(s"   Found ${flows.size} data flow paths from sources to system() arguments")

  flows.zipWithIndex.foreach { case (flow, flowIdx) =>
    println(s"\n--- Flow Path ${flowIdx + 1} ---")
    println(s"   Path length: ${flow.elements.size} nodes")

    flow.elements.zipWithIndex.foreach { case (element, elemIdx) =>
      println(s"\n   Step ${elemIdx + 1}:")
      printNodeDetails(element, "      ")

      // Print edges to next element if not last
      if (elemIdx < flow.elements.size - 1) {
        println(s"      ⬇️  Flow continues...")
      }
    }
  }

  // DDG (Data Dependence Graph) Analysis
  println(s"\n📊 DATA DEPENDENCE GRAPH ANALYSIS:")

  // Analyze DDG incoming edges for system arguments
  systemArguments.foreach { arg =>
    println(s"\n--- DDG Analysis for System Argument ---")
    printNodeDetails(arg, "   ")

    // Get DDG incoming nodes
    val ddgInNodes = arg.ddgIn
    println(s"   DDG incoming nodes: ${ddgInNodes.size}")

    ddgInNodes.zipWithIndex.foreach { case (ddgNode, ddgIdx) =>
      println(s"\n   DDG In ${ddgIdx + 1}:")
      printNodeDetails(ddgNode, "      ")
    }
  }

  // CFG (Control Flow Graph) Analysis
  println(s"\n🔄 CONTROL FLOW GRAPH ANALYSIS:")

  systemCalls.foreach { call =>
    println(s"\n--- CFG Analysis for System Call ---")
    printNodeDetails(call, "   ")

    // CFG predecessors
    val cfgPrev = call.cfgPrev
    println(s"   CFG previous nodes: ${cfgPrev.size}")
    cfgPrev.zipWithIndex.foreach { case (prevNode, prevIdx) =>
      println(s"\n   CFG Prev ${prevIdx + 1}:")
      printNodeDetails(prevNode, "      ")
    }

    // CFG successors
    val cfgNext = call.cfgNext
    println(s"   CFG next nodes: ${cfgNext.size}")
    cfgNext.zipWithIndex.foreach { case (nextNode, nextIdx) =>
      println(s"\n   CFG Next ${nextIdx + 1}:")
      printNodeDetails(nextNode, "      ")
    }
  }

  // AST (Abstract Syntax Tree) Analysis
  println(s"\n🌳 ABSTRACT SYNTAX TREE ANALYSIS:")

  systemCalls.foreach { call =>
    println(s"\n--- AST Analysis for System Call ---")
    printNodeDetails(call, "   ")

    // AST parent
    val astParent = call.astParent
    println(s"   AST parent nodes: ${astParent.size}")
    astParent.foreach { parent =>
      println(s"\n   AST Parent:")
      printNodeDetails(parent, "      ")
    }

    // AST children
    val astChildren = call.astChildren
    println(s"   AST children nodes: ${astChildren.size}")
    astChildren.zipWithIndex.foreach { case (child, childIdx) =>
      println(s"\n   AST Child ${childIdx + 1}:")
      printNodeDetails(child, "      ")
    }
  }

  // Method-level analysis
  println(s"\n🔧 METHOD-LEVEL ANALYSIS:")

  val methods = cpg.method.internal
  methods.foreach { method =>
    println(s"\n--- Method: ${method.name} ---")
    printNodeDetails(method, "   ")

    // Check if method contains our sources or sinks
    val methodSources = method.ast.intersect(sources.start)
    val methodSinks = method.ast.intersect(systemCalls.start)

    println(s"   Contains sources: ${methodSources.size}")
    println(s"   Contains sinks: ${methodSinks.size}")

    if (methodSources.nonEmpty || methodSinks.nonEmpty) {
      println(s"   ⚠️  This method is involved in the data flow!")

      // Method parameters
      println(s"   Parameters: ${method.parameter.size}")
      method.parameter.foreach { param =>
        println(s"      Parameter: ${param.name} (${param.typeFullName})")
      }

      // Method locals
      println(s"   Local variables: ${method.local.size}")
      method.local.foreach { local =>
        println(s"      Local: ${local.name} (${local.typeFullName})")
      }
    }
  }

  // Edge analysis - examine all edges in the graph related to our nodes
  println(s"\n🔗 EDGE ANALYSIS:")

  // Get all nodes involved in our analysis
  val allRelevantNodes = (sources ++ systemCalls ++ systemArguments).dedup

  allRelevantNodes.zipWithIndex.foreach { case (node, nodeIdx) =>
    println(s"\n--- Edge Analysis for Node ${nodeIdx + 1} ---")
    printNodeDetails(node, "   ")

    // Examine outgoing edges
    node match {
      case storedNode: io.shiftleft.codepropertygraph.generated.nodes.StoredNode =>
        println(s"   Outgoing edges:")
        storedNode._astOut.foreach { target =>
          println(s"      AST -> ${target.label}: ${target.code}")
        }
        storedNode._cfgOut.foreach { target =>
          println(s"      CFG -> ${target.label}: ${target.code}")
        }

        println(s"   Incoming edges:")
        storedNode._astIn.foreach { source =>
          println(s"      AST <- ${source.label}: ${source.code}")
        }
        storedNode._cfgIn.foreach { source =>
          println(s"      CFG <- ${source.label}: ${source.code}")
        }
      case _ =>
        println(s"   Cannot analyze edges for this node type")
    }
  }

  // Variable tracking analysis
  println(s"\n📝 VARIABLE TRACKING ANALYSIS:")

  // Track the 'cmd' variable specifically
  val cmdIdentifiers = cpg.identifier.name("cmd")
  println(s"   'cmd' identifiers found: ${cmdIdentifiers.size}")

  cmdIdentifiers.zipWithIndex.foreach { case (cmdId, cmdIdx) =>
    println(s"\n--- 'cmd' Variable ${cmdIdx + 1} ---")
    printNodeDetails(cmdId, "   ")

    // Check if this cmd identifier reaches system
    val reachesSystem = systemArguments.reachableBy(cmdId.start)
    println(s"   Reaches system(): ${reachesSystem.size > 0}")

    if (reachesSystem.nonEmpty) {
      println(s"   ⚠️  This 'cmd' variable flows to system()!")
    }
  }

  // Alternative reachability analysis
  println(s"\n🔄 ALTERNATIVE REACHABILITY ANALYSIS:")

  // Check reachability using different approaches
  val directReachable = systemArguments.reachableBy(sources)
  println(s"   Direct reachability: ${directReachable.size} sink nodes reached")

  val anyGetReachable = systemArguments.reachableBy(allGetSources)
  println(s"   Any \$_GET reachability: ${anyGetReachable.size} sink nodes reached")

  val cmdVarReachable = systemArguments.reachableBy(cmdIdentifiers)
  println(s"   'cmd' variable reachability: ${cmdVarReachable.size} sink nodes reached")

  // Generate DOT graphs for visualization (if available)
  println(s"\n📊 GRAPH VISUALIZATION:")

  try {
    // Try to generate DDG for methods containing our flows
    val relevantMethods = cpg.method.internal.filter { method =>
      val methodNodes = method.ast
      methodNodes.intersect(sources.start).nonEmpty || methodNodes.intersect(systemCalls.start).nonEmpty
    }

    println(s"   Methods involved in data flow: ${relevantMethods.size}")
    relevantMethods.foreach { method =>
      println(s"      Method: ${method.name} (${method.filename}:${method.lineNumber.getOrElse("?")})")

      // Generate DDG dot representation
      try {
        val ddgDot = method.dotDdg.headOption
        if (ddgDot.isDefined) {
          println(s"         DDG generated successfully (${ddgDot.get.length} characters)")
        }
      } catch {
        case e: Exception =>
          println(s"         DDG generation failed: ${e.getMessage}")
      }
    }
  } catch {
    case e: Exception =>
      println(s"   Graph visualization failed: ${e.getMessage}")
  }

  // Summary and vulnerability assessment
  println(s"\n" + "=" * 80)
  println("VULNERABILITY ASSESSMENT SUMMARY")
  println("=" * 80)

  val hasDataFlow = flows.nonEmpty
  val hasDirectFlow = directReachable.nonEmpty
  val hasGetFlow = anyGetReachable.nonEmpty
  val hasCmdFlow = cmdVarReachable.nonEmpty

  println(s"📊 Flow Analysis Results:")
  println(s"   Detailed flows found: ${flows.size}")
  println(s"   Direct reachability: ${hasDirectFlow}")
  println(s"   Any \$_GET reachability: ${hasGetFlow}")
  println(s"   'cmd' variable reachability: ${hasCmdFlow}")

  if (hasDataFlow || hasDirectFlow || hasGetFlow || hasCmdFlow) {
    println(s"\n🚨 VULNERABILITY DETECTED!")
    println(s"   Risk Level: HIGH")
    println(s"   Type: Command Injection (CWE-77)")
    println(s"   Description: User input from \$_GET flows to system() function")
    println(s"   Recommendation: Validate and sanitize input before passing to system()")
  } else {
    println(s"\n✅ No direct data flow detected")
    println(s"   Note: This doesn't guarantee the code is safe")
  }

  println(s"\n📈 Analysis Statistics:")
  println(s"   Total sources analyzed: ${sources.size}")
  println(s"   Total sinks analyzed: ${systemCalls.size}")
  println(s"   Total methods analyzed: ${methods.size}")
  println(s"   Total flows found: ${flows.size}")

  // Return comprehensive results
  Map(
    "flows" -> flows,
    "sources" -> sources.l,
    "sinks" -> systemCalls.l,
    "hasVulnerability" -> (hasDataFlow || hasDirectFlow || hasGetFlow || hasCmdFlow),
    "riskLevel" -> (if (hasDataFlow || hasDirectFlow || hasGetFlow || hasCmdFlow) "HIGH" else "LOW"),
    "flowCount" -> flows.size,
    "sourceCount" -> sources.size,
    "sinkCount" -> systemCalls.size
  )
}