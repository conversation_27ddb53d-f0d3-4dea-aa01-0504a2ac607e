// <PERSON><PERSON> script to track data flow from $_GET['cmd'] to system() function
// This script detects command injection vulnerabilities in PHP code

@main def main(inputPath: String) = {
  // Import the PHP code for analysis
  importCode(inputPath)

  // Define source: $_GET['cmd'] access
  // In Joern, $_GET access is represented as assignment operations
  // Try multiple patterns to catch different representations
  def source = cpg.assignment.source.code(".*_GET.*cmd.*") ++
               cpg.call.code(".*_GET.*cmd.*") ++
               cpg.identifier.code(".*_GET.*cmd.*")

  // Define sink: system() function calls
  def sink = cpg.call.name("system").argument

  // Find data flows from source to sink
  val flows = sink.reachableByFlows(source)

  // Print the results
  println(s"Found ${flows.size} data flow paths from \$_GET['cmd'] to system():")

  flows.foreach { flow =>
    println("\n=== Data Flow Path ===")
    flow.elements.foreach { element =>
      println(s"${element.lineNumber.getOrElse("?")}:${element.columnNumber.getOrElse("?")} - ${element.code}")
    }
    println("=====================")
  }

  // Also check for reachability without detailed flows
  val reachable = sink.reachableBy(source)
  println(s"\nTotal reachable sink nodes: ${reachable.size}")

  // Print detailed information about sources and sinks
  println(s"\nSource nodes found: ${source.size}")
  source.foreach { s =>
    println(s"  Source: ${s.code} at line ${s.lineNumber.getOrElse("?")}")
  }

  println(s"\nSink nodes found: ${sink.size}")
  sink.foreach { s =>
    println(s"  Sink: ${s.code} at line ${s.lineNumber.getOrElse("?")}")
  }

  // Alternative approach: Check for any $_GET to system flow
  println("\n=== Alternative Analysis ===")
  def anyGetSource = cpg.assignment.source.code(".*_GET.*")
  def systemSink = cpg.call.name("system")
  val anyGetFlows = systemSink.reachableByFlows(anyGetSource)
  println(s"Any \$_GET to system() flows: ${anyGetFlows.size}")

  // Check for variable flows (cmd variable specifically)
  def cmdVariable = cpg.identifier.name("cmd")
  val cmdFlows = systemSink.reachableByFlows(cmdVariable)
  println(s"Variable 'cmd' to system() flows: ${cmdFlows.size}")

  // Return the flows for further analysis if needed
  flows
}