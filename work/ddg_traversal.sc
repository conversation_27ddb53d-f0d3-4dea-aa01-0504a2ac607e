// Simplified <PERSON><PERSON> script to traverse all CPG nodes and extract DDG information
// Saves results to JSON file in work directory

import io.joern.dataflowengineoss.language.*
import io.shiftleft.semanticcpg.language.*
import java.io.{FileWriter, PrintWriter}
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

// Simplified data structure
case class SimpleNodeInfo(
  nodeId: Long,
  nodeType: String,
  nodeString: String,
  ddgInCount: Int,
  ddgInNodes: List[String]
)

@main def main(inputPath: String) = {
  // Import the PHP code for analysis
  importCode(inputPath)

  println("=" * 80)
  println("SIMPLIFIED CPG NODES DDG ANALYSIS")
  println("=" * 80)

  val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"))
  val jsonFileName = s"work/ddg_analysis_${timestamp}.json"

  println(s"Starting DDG analysis...")
  println(s"Results will be saved to: ${jsonFileName}")

  // Get all nodes in the CPG
  val allNodes = cpg.all.l
  println(s"Total nodes in CPG: ${allNodes.size}")

  var results: List[SimpleNodeInfo] = List()
  var processedCount = 0

  println(s"\nProcessing nodes...")

  // Process each node with simplified approach
  allNodes.foreach { node =>
    processedCount += 1
    if (processedCount % 100 == 0) {
      println(s"Processed ${processedCount}/${allNodes.size} nodes...")
    }

    try {
      val nodeId = node.id
      val nodeType = node.label
      val nodeString = node.toString

      // Try to get DDG information safely
      val (ddgCount, ddgNodeStrings) = try {
        // Try different DDG methods
        val ddgNodes = try {
          // First try the most basic approach
          val inNodes = cpg.all.filter(_.id == nodeId).ddgIn.l
          (inNodes.size, inNodes.map(_.toString).take(10)) // Limit to 10 for performance
        } catch {
          case _: Exception =>
            // Fallback: just count and basic info
            (0, List[String]())
        }
        ddgNodes
      } catch {
        case e: Exception =>
          if (processedCount % 1000 == 0) {
            println(s"DDG error for node ${nodeId}: ${e.getMessage}")
          }
          (0, List[String]())
      }

      // Add to results
      results = results :+ SimpleNodeInfo(
        nodeId = nodeId,
        nodeType = nodeType,
        nodeString = nodeString,
        ddgInCount = ddgCount,
        ddgInNodes = ddgNodeStrings
      )

    } catch {
      case e: Exception =>
        println(s"Error processing node ${node.id}: ${e.getMessage}")
    }
  }

  println(s"\nCompleted processing ${processedCount} nodes")
  println(s"Results collected: ${results.size}")

  // Save to JSON file
  println(s"\nSaving JSON file...")
  saveSimpleJSON(results, jsonFileName)

  println(s"\nAnalysis complete!")
  println(s"File saved: ${jsonFileName}")

  // Return summary
  Map(
    "totalNodes" -> allNodes.size,
    "processedNodes" -> results.size,
    "jsonFile" -> jsonFileName,
    "nodesWithDDG" -> results.count(_.ddgInCount > 0),
    "timestamp" -> timestamp
  )
}

// Simplified function to save results to JSON
def saveSimpleJSON(results: List[SimpleNodeInfo], fileName: String): Unit = {
  val writer = new PrintWriter(new FileWriter(fileName))
  try {
    writer.println("{")
    writer.println(s"""  "metadata": {""")
    writer.println(s"""    "timestamp": "${LocalDateTime.now()}",""")
    writer.println(s"""    "totalNodes": ${results.size},""")
    writer.println(s"""    "nodesWithDDG": ${results.count(_.ddgInCount > 0)}""")
    writer.println(s"""  },""")
    writer.println(s"""  "nodes": [""")

    results.zipWithIndex.foreach { case (node, index) =>
      writer.println(s"""    {""")
      writer.println(s"""      "nodeId": ${node.nodeId},""")
      writer.println(s"""      "nodeType": "${escapeJson(node.nodeType)}",""")
      writer.println(s"""      "nodeString": "${escapeJson(node.nodeString)}",""")
      writer.println(s"""      "ddgInCount": ${node.ddgInCount},""")
      writer.println(s"""      "ddgInNodes": [""")

      node.ddgInNodes.zipWithIndex.foreach { case (ddgNode, ddgIndex) =>
        writer.print(s"""        "${escapeJson(ddgNode)}"""")
        if (ddgIndex < node.ddgInNodes.size - 1) writer.println(",")
        else writer.println()
      }

      writer.print(s"""      ]""")
      writer.print(s"""    }""")
      if (index < results.size - 1) writer.println(",")
      else writer.println()
    }

    writer.println(s"""  ]""")
    writer.println("}")
  } finally {
    writer.close()
  }
}

// Function to escape JSON strings
def escapeJson(str: String): String = {
  str.replace("\\", "\\\\")
     .replace("\"", "\\\"")
     .replace("\n", "\\n")
     .replace("\r", "\\r")
     .replace("\t", "\\t")
}

// Function to save results to HTML
def saveToHTML(results: List[NodeDDGInfo], fileName: String): Unit = {
  val writer = new PrintWriter(new FileWriter(fileName))
  try {
    writer.println("<!DOCTYPE html>")
    writer.println("<html lang=\"en\">")
    writer.println("<head>")
    writer.println("    <meta charset=\"UTF-8\">")
    writer.println("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">")
    writer.println("    <title>DDG Analysis Results</title>")
    writer.println("    <style>")
    writer.println("        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }")
    writer.println("        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }")
    writer.println("        h1 { color: #333; text-align: center; border-bottom: 2px solid #007acc; padding-bottom: 10px; }")
    writer.println("        .summary { background: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px; }")
    writer.println("        .node { border: 1px solid #ddd; margin: 10px 0; border-radius: 5px; overflow: hidden; }")
    writer.println("        .node-header { background: #007acc; color: white; padding: 10px; cursor: pointer; }")
    writer.println("        .node-header:hover { background: #005a9e; }")
    writer.println("        .node-content { padding: 15px; display: none; }")
    writer.println("        .node-info { display: grid; grid-template-columns: 150px 1fr; gap: 10px; margin-bottom: 15px; }")
    writer.println("        .label { font-weight: bold; color: #555; }")
    writer.println("        .value { color: #333; }")
    writer.println("        .ddg-elements { margin-top: 15px; }")
    writer.println("        .ddg-element { background: #f8f9fa; border: 1px solid #e9ecef; margin: 5px 0; padding: 10px; border-radius: 3px; }")
    writer.println("        .ddg-element-header { font-weight: bold; color: #495057; margin-bottom: 5px; }")
    writer.println("        .ddg-element-info { font-size: 0.9em; color: #6c757d; }")
    writer.println("        .code { font-family: 'Courier New', monospace; background: #f1f1f1; padding: 2px 4px; border-radius: 2px; }")
    writer.println("        .no-ddg { color: #999; font-style: italic; }")
    writer.println("        .stats { display: flex; justify-content: space-around; text-align: center; }")
    writer.println("        .stat { background: white; padding: 10px; border-radius: 5px; border: 1px solid #ddd; }")
    writer.println("        .stat-number { font-size: 2em; font-weight: bold; color: #007acc; }")
    writer.println("        .stat-label { color: #666; }")
    writer.println("    </style>")
    writer.println("    <script>")
    writer.println("        function toggleNode(nodeId) {")
    writer.println("            const content = document.getElementById('content-' + nodeId);")
    writer.println("            content.style.display = content.style.display === 'none' ? 'block' : 'none';")
    writer.println("        }")
    writer.println("        function expandAll() {")
    writer.println("            const contents = document.querySelectorAll('.node-content');")
    writer.println("            contents.forEach(content => content.style.display = 'block');")
    writer.println("        }")
    writer.println("        function collapseAll() {")
    writer.println("            const contents = document.querySelectorAll('.node-content');")
    writer.println("            contents.forEach(content => content.style.display = 'none');")
    writer.println("        }")
    writer.println("    </script>")
    writer.println("</head>")
    writer.println("<body>")
    writer.println("    <div class=\"container\">")
    writer.println("        <h1>🔍 DDG Analysis Results</h1>")

    // Summary section
    val nodesWithDDG = results.count(_.ddgInPathElements.nonEmpty)
    val totalDDGElements = results.map(_.ddgInPathElements.size).sum.toInt

    writer.println("        <div class=\"summary\">")
    writer.println("            <h2>📊 Analysis Summary</h2>")
    writer.println("            <div class=\"stats\">")
    writer.println(s"                <div class=\"stat\">")
    writer.println(s"                    <div class=\"stat-number\">${results.size}</div>")
    writer.println(s"                    <div class=\"stat-label\">Total Nodes</div>")
    writer.println(s"                </div>")
    writer.println(s"                <div class=\"stat\">")
    writer.println(s"                    <div class=\"stat-number\">${nodesWithDDG}</div>")
    writer.println(s"                    <div class=\"stat-label\">Nodes with DDG</div>")
    writer.println(s"                </div>")
    writer.println(s"                <div class=\"stat\">")
    writer.println(s"                    <div class=\"stat-number\">${totalDDGElements}</div>")
    writer.println(s"                    <div class=\"stat-label\">Total DDG Elements</div>")
    writer.println(s"                </div>")
    writer.println("            </div>")
    writer.println(s"            <p><strong>Generated:</strong> ${LocalDateTime.now()}</p>")
    writer.println("            <div style=\"margin-top: 10px;\">")
    writer.println("                <button onclick=\"expandAll()\" style=\"margin-right: 10px; padding: 5px 10px;\">Expand All</button>")
    writer.println("                <button onclick=\"collapseAll()\" style=\"padding: 5px 10px;\">Collapse All</button>")
    writer.println("            </div>")
    writer.println("        </div>")

    // Nodes section
    writer.println("        <h2>🔗 Node Details</h2>")

    results.foreach { node =>
      writer.println(s"        <div class=\"node\">")
      writer.println(s"            <div class=\"node-header\" onclick=\"toggleNode(${node.nodeId})\">")
      writer.println(s"                📋 Node ${node.nodeId} (${escapeHtml(node.nodeType)}) - ${node.ddgInPathElements.size} DDG elements")
      writer.println(s"            </div>")
      writer.println(s"            <div id=\"content-${node.nodeId}\" class=\"node-content\">")
      writer.println(s"                <div class=\"node-info\">")
      writer.println(s"                    <div class=\"label\">Node ID:</div>")
      writer.println(s"                    <div class=\"value\">${node.nodeId}</div>")
      writer.println(s"                    <div class=\"label\">Type:</div>")
      writer.println(s"                    <div class=\"value\">${escapeHtml(node.nodeType)}</div>")
      writer.println(s"                    <div class=\"label\">Code:</div>")
      writer.println(s"                    <div class=\"value\"><span class=\"code\">${escapeHtml(node.code)}</span></div>")
      writer.println(s"                    <div class=\"label\">Line:</div>")
      writer.println(s"                    <div class=\"value\">${node.lineNumber.getOrElse("N/A")}</div>")
      writer.println(s"                    <div class=\"label\">Method:</div>")
      writer.println(s"                    <div class=\"value\">${escapeHtml(node.methodName)}</div>")
      writer.println(s"                    <div class=\"label\">File:</div>")
      writer.println(s"                    <div class=\"value\">${escapeHtml(node.fileName)}</div>")
      writer.println(s"                </div>")

      if (node.ddgInPathElements.nonEmpty) {
        writer.println(s"                <div class=\"ddg-elements\">")
        writer.println(s"                    <h4>🔄 DDG Incoming Path Elements (${node.ddgInPathElements.size}):</h4>")
        node.ddgInPathElements.zipWithIndex.foreach { case (elem, index) =>
          writer.println(s"                    <div class=\"ddg-element\">")
          writer.println(s"                        <div class=\"ddg-element-header\">${index + 1}. ${escapeHtml(elem.elementType)} (ID: ${elem.elementId})</div>")
          writer.println(s"                        <div class=\"ddg-element-info\">")
          writer.println(s"                            <strong>Code:</strong> <span class=\"code\">${escapeHtml(elem.elementCode)}</span><br>")
          writer.println(s"                            <strong>Line:</strong> ${elem.elementLine.getOrElse("N/A")} | ")
          writer.println(s"                            <strong>Method:</strong> ${escapeHtml(elem.elementMethod)} | ")
          writer.println(s"                            <strong>File:</strong> ${escapeHtml(elem.elementFile)}")
          writer.println(s"                        </div>")
          writer.println(s"                    </div>")
        }
        writer.println(s"                </div>")
      } else {
        writer.println(s"                <div class=\"no-ddg\">No DDG incoming path elements found for this node.</div>")
      }

      writer.println(s"            </div>")
      writer.println(s"        </div>")
    }

    writer.println("    </div>")
    writer.println("</body>")
    writer.println("</html>")
  } finally {
    writer.close()
  }
}

// Function to escape HTML strings
def escapeHtml(str: String): String = {
  str.replace("&", "&amp;")
     .replace("<", "&lt;")
     .replace(">", "&gt;")
     .replace("\"", "&quot;")
     .replace("'", "&#x27;")
}
