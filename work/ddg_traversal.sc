// Simplified <PERSON><PERSON> script to traverse all CPG nodes and extract DDG information
// Saves results to JSON file in work directory

import io.joern.dataflowengineoss.language.*
import io.shiftleft.semanticcpg.language.*
import java.io.{FileWriter, PrintWriter}
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

// Simplified data structure
case class SimpleNodeInfo(
  nodeId: Long,
  nodeType: String,
  nodeString: String,
  ddgInCount: Int,
  ddgInNodes: List[String]
)

@main def main(inputPath: String) = {
  // Import the PHP code for analysis
  importCode(inputPath)

  println("=" * 80)
  println("SIMPLIFIED CPG NODES DDG ANALYSIS")
  println("=" * 80)

  val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"))
  val jsonFileName = s"work/ddg_analysis_${timestamp}.json"

  println(s"Starting DDG analysis...")
  println(s"Results will be saved to: ${jsonFileName}")

  // Get all nodes in the CPG
  val allNodes = cpg.all.l
  println(s"Total nodes in CPG: ${allNodes.size}")

  var results: List[SimpleNodeInfo] = List()
  var processedCount = 0

  println(s"\nProcessing nodes...")

  // Process each node with simplified approach
  allNodes.foreach { node =>
    processedCount += 1
    if (processedCount % 100 == 0) {
      println(s"Processed ${processedCount}/${allNodes.size} nodes...")
    }

    try {
      val nodeId = node.id
      val nodeType = node.label
      val nodeString = node.toString

      // Try to get DDG information using correct traversal context
      val (ddgCount, ddgNodeStrings) = try {
        // Use proper traversal context for DDG methods
        val ddgNodes = try {
          // Method 1: Use ddgInPathElem in traversal context
          val pathElements = cpg.all.id(nodeId).ddgInPathElem.l
          val nodeStrings = pathElements.map(pe => s"${pe.node.label}:${pe.node.code}").take(10)
          (pathElements.size, nodeStrings)
        } catch {
          case _: Exception =>
            try {
              // Method 2: Use ddgIn in traversal context
              val inNodes = cpg.all.id(nodeId).ddgIn.l
              (inNodes.size, inNodes.map(_.toString).take(10))
            } catch {
              case _: Exception =>
                try {
                  // Method 3: Use basic incoming edges as fallback
                  val inNodes = node._astIn.l ++ node._cfgIn.l
                  val uniqueNodes = inNodes.distinct
                  (uniqueNodes.size, uniqueNodes.map(_.toString).take(10))
                } catch {
                  case _: Exception =>
                    // Method 4: Record that we tried but couldn't get DDG info
                    (0, List[String]("No DDG information available"))
                }
            }
        }
        ddgNodes
      } catch {
        case e: Exception =>
          if (processedCount % 1000 == 0) {
            println(s"DDG error for node ${nodeId}: ${e.getMessage}")
          }
          (0, List[String]())
      }

      // Add to results
      results = results :+ SimpleNodeInfo(
        nodeId = nodeId,
        nodeType = nodeType,
        nodeString = nodeString,
        ddgInCount = ddgCount,
        ddgInNodes = ddgNodeStrings
      )

    } catch {
      case e: Exception =>
        println(s"Error processing node ${node.id}: ${e.getMessage}")
    }
  }

  println(s"\nCompleted processing ${processedCount} nodes")
  println(s"Results collected: ${results.size}")

  // Save to JSON file
  println(s"\nSaving JSON file...")
  saveSimpleJSON(results, jsonFileName)

  println(s"\nAnalysis complete!")
  println(s"File saved: ${jsonFileName}")

  // Return summary
  Map(
    "totalNodes" -> allNodes.size,
    "processedNodes" -> results.size,
    "jsonFile" -> jsonFileName,
    "nodesWithDDG" -> results.count(_.ddgInCount > 0),
    "timestamp" -> timestamp
  )
}

// Simplified function to save results to JSON
def saveSimpleJSON(results: List[SimpleNodeInfo], fileName: String): Unit = {
  val writer = new PrintWriter(new FileWriter(fileName))
  try {
    writer.println("{")
    writer.println(s"""  "metadata": {""")
    writer.println(s"""    "timestamp": "${LocalDateTime.now()}",""")
    writer.println(s"""    "totalNodes": ${results.size},""")
    writer.println(s"""    "nodesWithDDG": ${results.count(_.ddgInCount > 0)}""")
    writer.println(s"""  },""")
    writer.println(s"""  "nodes": [""")

    results.zipWithIndex.foreach { case (node, index) =>
      writer.println(s"""    {""")
      writer.println(s"""      "nodeId": ${node.nodeId},""")
      writer.println(s"""      "nodeType": "${escapeJson(node.nodeType)}",""")
      writer.println(s"""      "nodeString": "${escapeJson(node.nodeString)}",""")
      writer.println(s"""      "ddgInCount": ${node.ddgInCount},""")
      writer.println(s"""      "ddgInNodes": [""")

      node.ddgInNodes.zipWithIndex.foreach { case (ddgNode, ddgIndex) =>
        writer.print(s"""        "${escapeJson(ddgNode)}"""")
        if (ddgIndex < node.ddgInNodes.size - 1) writer.println(",")
        else writer.println()
      }

      writer.print(s"""      ]""")
      writer.print(s"""    }""")
      if (index < results.size - 1) writer.println(",")
      else writer.println()
    }

    writer.println(s"""  ]""")
    writer.println("}")
  } finally {
    writer.close()
  }
}

// Function to escape JSON strings
def escapeJson(str: String): String = {
  str.replace("\\", "\\\\")
     .replace("\"", "\\\"")
     .replace("\n", "\\n")
     .replace("\r", "\\r")
     .replace("\t", "\\t")
}


