cpg.identifier.name("_GET").out.l
……
Identifier(
    argumentIndex = 1,
    argumentName = None,
    code = "$cmd",
    columnNumber = None,
    dynamicTypeHintFullName = IndexedSeq(),
    lineNumber = Some(value = 13),
    name = "cmd",
    offset = None,
    offsetEnd = None,
    order = 1,
    possibleTypes = IndexedSeq(),
    typeFullName = "ANY"
  )


cpg.identifier.name("cmd").out.l
……
Call(
    argumentIndex = -1,
    argumentName = None,
    code = "SimpleClass->__construct($cmd)",
    columnNumber = None,
    dispatchType = "DYNAMIC_DISPATCH",
    dynamicTypeHintFullName = IndexedSeq("SimpleClass->__construct"),
    lineNumber = Some(value = 14),
    methodFullName = "SimpleClass->__construct",
    name = "__construct",
    offset = None,
    offsetEnd = None,
    order = 2,
    possibleTypes = IndexedSeq(),
    signature = "<unresolvedSignature>(1)",
    typeFullName = "ANY"
  )


cpg.call.name("__construct").out.l
……
Method(
    astParentFullName = "SimpleClass",
    astParentType = "TYPE_DECL",
    code = "PUBLIC function __construct(this,$name)",
    columnNumber = None,
    columnNumberEnd = None,
    filename = "case3.php",
    fullName = "SimpleClass->__construct",
    genericSignature = "<empty>",
    hash = None,
    isExternal = false,
    lineNumber = Some(value = 5),
    lineNumberEnd = None,
    name = "__construct",
    offset = None,
    offsetEnd = None,
    order = 2,
    signature = "<unresolvedSignature>(1)"
  )


cpg.method.name("__construct").out.l
……
Call(
    argumentIndex = 1,
    argumentName = None,
    code = "$this->name",
    columnNumber = None,
    dispatchType = "STATIC_DISPATCH",
    dynamicTypeHintFullName = IndexedSeq(),
    lineNumber = Some(value = 6),
    methodFullName = "<operator>.fieldAccess",
    name = "<operator>.fieldAccess",
    offset = None,
    offsetEnd = None,
    order = 1,
    possibleTypes = IndexedSeq(),
    signature = "",
    typeFullName = "ANY"
  )

可达边一
cpg.call.code("\\$this->name").out.l
……
Call(
    argumentIndex = -1,
    argumentName = None,
    code = "system($this->name)",
    columnNumber = None,
    dispatchType = "STATIC_DISPATCH",
    dynamicTypeHintFullName = IndexedSeq("system"),
    lineNumber = Some(value = 10),
    methodFullName = "system",
    name = "system",
    offset = None,
    offsetEnd = None,
    order = 1,
    possibleTypes = IndexedSeq(),
    signature = "<unresolvedSignature>(1)",
    typeFullName = "ANY"
  )
……

可达边二
cpg.identifier.name("this").out.l
Method(
    astParentFullName = "<global>",
    astParentType = "NAMESPACE_BLOCK",
    code = "<empty>",
    columnNumber = None,
    columnNumberEnd = None,
    filename = "<empty>",
    fullName = """<unresolvedNamespace>\$instance->greet""",
    genericSignature = "<empty>",
    hash = None,
    isExternal = true,
    lineNumber = None,
    lineNumberEnd = None,
    name = "greet",
    offset = None,
    offsetEnd = None,
    order = 0,
    signature = "<unresolvedSignature>(1)"
  )


cpg.method.name("greet").out.l
……
Call(
    argumentIndex = -1,
    argumentName = None,
    code = "system($cmd)",
    columnNumber = None,
    dispatchType = "STATIC_DISPATCH",
    dynamicTypeHintFullName = IndexedSeq("system"),
    lineNumber = Some(value = 4),
    methodFullName = "system",
    name = "system",
    offset = None,
    offsetEnd = None,
    order = 1,
    possibleTypes = IndexedSeq(),
    signature = "<unresolvedSignature>(1)",
    typeFullName = "ANY"
  )

  我尝试过如下情况都是可达的
  情况一
val source = cpg.identifier.name("this").out.l
val sink = cpg.call.name("system").l
val flows = sink.reachableByFlows(source)  
flows.p
val res74: List[String] = List(
  """
┌─────────────────┬───────────────────┬────┬──────┬─────────┐                                                                                                                                                                               
│nodeType         │tracked            │line│method│file     │                                                                                                                                                                               
├─────────────────┼───────────────────┼────┼──────┼─────────┤                                                                                                                                                                               
│MethodParameterIn│greet(this)        │9   │greet │case3.php│                                                                                                                                                                               
│Call             │system($this->name)│10  │greet │case3.php│                                                                                                                                                                               
│Call             │system($this->name)│10  │greet │case3.php│                                                                                                                                                                               
└─────────────────┴───────────────────┴────┴──────┴─────────┘"""                                                                                                                                                                            
)
情况二
val source = cpg.call.code("\\$this->name").out.l
val sink = cpg.call.name("system").l
val flows = sink.reachableByFlows(source)  
flows.p
val res62: List[String] = List(
  """
┌────────┬───────────────────┬────┬──────┬─────────┐                                                                                                                                                                                        
│nodeType│tracked            │line│method│file     │                                                                                                                                                                                        
├────────┼───────────────────┼────┼──────┼─────────┤                                                                                                                                                                                        
│Call    │system($this->name)│10  │greet │case3.php│                                                                                                                                                                                        
└────────┴───────────────────┴────┴──────┴─────────┘""",                                                                                                                                                                                    
  """
┌──────────┬───────────────────┬────┬──────┬─────────┐                                                                                                                                                                                      
│nodeType  │tracked            │line│method│file     │                                                                                                                                                                                      
├──────────┼───────────────────┼────┼──────┼─────────┤                                                                                                                                                                                      
│Identifier│system($this->name)│10  │greet │case3.php│                                                                                                                                                                                      
│Call      │system($this->name)│10  │greet │case3.php│                                                                                                                                                                                      
│Call      │system($this->name)│10  │greet │case3.php│                                                                                                                                                                                      
└──────────┴───────────────────┴────┴──────┴─────────┘"""                                                                                                                                                                                   
)

为什么下面这种情况就不行