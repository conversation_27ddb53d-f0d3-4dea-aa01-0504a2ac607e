// <PERSON><PERSON> script for filtering data flows from $_GET to system() in PHP
// Based on the analysis in bj file - tracks through class instantiation and method calls

import io.joern.dataflowengineoss.language.*
import io.shiftleft.semanticcpg.language.*

@main def main(inputPath: String) = {
  // Import the PHP code for analysis
  importCode(inputPath)
  
  println("=" * 80)
  println("PHP $_GET TO SYSTEM FLOW FILTER")
  println("=" * 80)
  
  // Define multiple source patterns based on your analysis
  def getIdentifiers = cpg.identifier.name("_GET")
  def cmdIdentifiers = cpg.identifier.name("cmd")
  def thisIdentifiers = cpg.identifier.name("this")
  
  // Define sink patterns
  def systemCalls = cpg.call.name("system")
  def systemArguments = systemCalls.argument
  
  println(s"\n📍 SOURCE PATTERNS:")
  println(s"   $_GET identifiers: ${getIdentifiers.size}")
  println(s"   cmd identifiers: ${cmdIdentifiers.size}")
  println(s"   this identifiers: ${thisIdentifiers.size}")
  
  println(s"\n🎯 SINK PATTERNS:")
  println(s"   system() calls: ${systemCalls.size}")
  println(s"   system() arguments: ${systemArguments.size}")
  
  // Flow Analysis 1: Direct $_GET to system
  println(s"\n🌊 FLOW ANALYSIS 1: Direct $_GET to system")
  val flow1 = systemCalls.reachableByFlows(getIdentifiers)
  println(s"   Flows found: ${flow1.size}")
  flow1.foreach { flow =>
    println(s"\n   Flow path:")
    flow.elements.foreach { element =>
      println(s"     ${element.label}: ${element.code} (Line: ${element.lineNumber.getOrElse("N/A")})")
    }
  }
  
  // Flow Analysis 2: cmd variable to system
  println(s"\n🌊 FLOW ANALYSIS 2: cmd variable to system")
  val flow2 = systemCalls.reachableByFlows(cmdIdentifiers)
  println(s"   Flows found: ${flow2.size}")
  flow2.foreach { flow =>
    println(s"\n   Flow path:")
    flow.elements.foreach { element =>
      println(s"     ${element.label}: ${element.code} (Line: ${element.lineNumber.getOrElse("N/A")})")
    }
  }
  
  // Flow Analysis 3: this to system (based on your successful case)
  println(s"\n🌊 FLOW ANALYSIS 3: this to system")
  val flow3 = systemCalls.reachableByFlows(thisIdentifiers)
  println(s"   Flows found: ${flow3.size}")
  flow3.foreach { flow =>
    println(s"\n   Flow path:")
    flow.elements.foreach { element =>
      println(s"     ${element.label}: ${element.code} (Line: ${element.lineNumber.getOrElse("N/A")})")
    }
  }
  
  // Flow Analysis 4: $this->name to system (based on your analysis)
  println(s"\n🌊 FLOW ANALYSIS 4: \$this->name to system")
  val thisNameCalls = cpg.call.code(".*this->name.*")
  val flow4 = systemCalls.reachableByFlows(thisNameCalls)
  println(s"   \$this->name calls found: ${thisNameCalls.size}")
  println(s"   Flows found: ${flow4.size}")
  flow4.foreach { flow =>
    println(s"\n   Flow path:")
    flow.elements.foreach { element =>
      println(s"     ${element.label}: ${element.code} (Line: ${element.lineNumber.getOrElse("N/A")})")
    }
  }
  
  // Flow Analysis 5: Constructor method flows
  println(s"\n🌊 FLOW ANALYSIS 5: Constructor method flows")
  val constructorMethods = cpg.method.name("__construct")
  val flow5 = systemCalls.reachableByFlows(constructorMethods)
  println(s"   Constructor methods found: ${constructorMethods.size}")
  println(s"   Flows found: ${flow5.size}")
  flow5.foreach { flow =>
    println(s"\n   Flow path:")
    flow.elements.foreach { element =>
      println(s"     ${element.label}: ${element.code} (Line: ${element.lineNumber.getOrElse("N/A")})")
    }
  }
  
  // Comprehensive reachability analysis
  println(s"\n🔄 COMPREHENSIVE REACHABILITY ANALYSIS:")
  
  // Check what can reach system calls
  val allSources = getIdentifiers ++ cmdIdentifiers ++ thisIdentifiers ++ thisNameCalls
  val reachableNodes = systemArguments.reachableBy(allSources)
  println(s"   Total reachable system arguments: ${reachableNodes.size}")
  
  // Detailed path analysis for each reachable node
  reachableNodes.zipWithIndex.foreach { case (node, idx) =>
    println(s"\n   Reachable Node ${idx + 1}:")
    println(s"     Code: ${node.code}")
    println(s"     Line: ${node.lineNumber.getOrElse("N/A")}")
    println(s"     Method: ${node.method.name.headOption.getOrElse("N/A")}")
  }
  
  // Step-by-step flow tracing (based on your manual analysis)
  println(s"\n🔍 STEP-BY-STEP FLOW TRACING:")
  
  // Step 1: Find $_GET['cmd'] assignment
  val getAssignments = cpg.assignment.target.code(".*cmd.*").source.code(".*_GET.*")
  println(s"\n   Step 1 - \$_GET['cmd'] assignments: ${getAssignments.size}")
  getAssignments.foreach { assignment =>
    println(s"     ${assignment.code} (Line: ${assignment.lineNumber.getOrElse("N/A")})")
  }
  
  // Step 2: Find constructor calls with cmd
  val constructorCalls = cpg.call.name("__construct").argument.code(".*cmd.*")
  println(s"\n   Step 2 - Constructor calls with cmd: ${constructorCalls.size}")
  constructorCalls.foreach { call =>
    println(s"     ${call.code} (Line: ${call.lineNumber.getOrElse("N/A")})")
  }
  
  // Step 3: Find field assignments in constructor
  val fieldAssignments = cpg.assignment.target.code(".*this->name.*")
  println(s"\n   Step 3 - Field assignments: ${fieldAssignments.size}")
  fieldAssignments.foreach { assignment =>
    println(s"     ${assignment.code} (Line: ${assignment.lineNumber.getOrElse("N/A")})")
  }
  
  // Step 4: Find system calls using the field
  val systemWithField = cpg.call.name("system").argument.code(".*this->name.*")
  println(s"\n   Step 4 - System calls with field: ${systemWithField.size}")
  systemWithField.foreach { call =>
    println(s"     ${call.code} (Line: ${call.lineNumber.getOrElse("N/A")})")
  }
  
  // Summary
  println(s"\n" + "=" * 80)
  println("FLOW FILTER SUMMARY")
  println("=" * 80)
  
  val totalFlows = flow1.size + flow2.size + flow3.size + flow4.size + flow5.size
  val hasVulnerability = totalFlows > 0 || reachableNodes.nonEmpty
  
  println(s"📊 Flow Analysis Results:")
  println(s"   Direct \$_GET flows: ${flow1.size}")
  println(s"   cmd variable flows: ${flow2.size}")
  println(s"   this identifier flows: ${flow3.size}")
  println(s"   \$this->name flows: ${flow4.size}")
  println(s"   Constructor flows: ${flow5.size}")
  println(s"   Total flows detected: ${totalFlows}")
  println(s"   Reachable nodes: ${reachableNodes.size}")
  
  if (hasVulnerability) {
    println(s"\n🚨 VULNERABILITY DETECTED!")
    println(s"   Risk Level: HIGH")
    println(s"   Type: Command Injection via Object Property")
    println(s"   Flow: \$_GET['cmd'] → \$cmd → constructor → \$this->name → system()")
  } else {
    println(s"\n✅ No flows detected")
  }
  
  // Return results for further processing
  Map(
    "directGetFlows" -> flow1,
    "cmdFlows" -> flow2,
    "thisFlows" -> flow3,
    "thisNameFlows" -> flow4,
    "constructorFlows" -> flow5,
    "reachableNodes" -> reachableNodes.l,
    "hasVulnerability" -> hasVulnerability,
    "totalFlows" -> totalFlows
  )
}
