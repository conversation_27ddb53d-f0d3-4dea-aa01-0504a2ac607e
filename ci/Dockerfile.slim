FROM almalinux/9-minimal:latest

LABEL maintainer="joern" \
      org.opencontainers.image.authors="<PERSON> Joern" \
      org.opencontainers.image.source="https://github.com/joernio/joern" \
      org.opencontainers.image.url="https://github.com/joernio/joern" \
      org.opencontainers.image.version="1.1.1643" \
      org.opencontainers.image.vendor="Joern" \
      org.opencontainers.image.licenses="Apache-2.0" \
      org.opencontainers.image.title="joern" \
      org.opencontainers.image.description="Joern is a platform for analyzing source code, bytecode, and binary executables" \
      org.opencontainers.docker.cmd="docker run --rm -it -v /tmp:/tmp -v $(pwd):/app:rw -w /app -t ghcr.io/joernio/joern-slim joern"

ENV JOERN_HOME=/opt/joern/joern-cli \
    LC_ALL=en_US.UTF-8 \
    LANG=en_US.UTF-8 \
    LANGUAGE=en_US.UTF-8 \
    JAVA_HOME="/etc/alternatives/jre_17" \
    JAVA_17_HOME="/etc/alternatives/jre_17" \
    JOERN_DATAFLOW_TRACKED_WIDTH=128 \
    CLASSPATH=$CLASSPATH:/usr/local/bin:/opt/joern/joern-cli/lib: \
    PATH=${PATH}:/opt/joern/joern-cli:/opt/joern/joern-cli/bin:${GOPATH}/bin:/usr/local/go/bin:/usr/local/bin:/root/.local/bin:${JAVA_HOME}/bin:

RUN microdnf install -y php php-cli which tar zip unzip sudo shadow-utils \
        java-17-openjdk-headless ncurses zlib graphviz glibc-common glibc-all-langpacks \
    && curl -LO https://github.com/joernio/joern/releases/latest/download/joern-install.sh \
    && chmod +x ./joern-install.sh \
    && ./joern-install.sh --without-plugins \
    && curl -LO https://github.com/qarmin/czkawka/releases/download/5.1.0/linux_czkawka_cli \
    && chmod +x linux_czkawka_cli \
    && ./linux_czkawka_cli dup -e /opt/joern/joern-cli/lib --directories /opt/joern/joern-cli/frontends -x jar -L -s hash -f results.txt -D HARD \
    && rm linux_czkawka_cli results.txt /opt/joern/joern-cli/frontends/jssrc2cpg/bin/astgen/astgen-macos-arm /opt/joern/joern-cli/frontends/jssrc2cpg/bin/astgen/astgen-macos /opt/joern/joern-cli/frontends/jssrc2cpg/bin/astgen/astgen-win.exe \
    && useradd -ms /bin/bash joern \
    && chown -R joern:joern /opt/joern \
    && rm /joern-cli.zip /joern-install.sh \
    && rm -rf /var/cache/yum \
    && microdnf clean all

WORKDIR /app

CMD [ "joern" ]
