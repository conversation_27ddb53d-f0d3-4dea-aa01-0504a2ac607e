name: release
on:
  schedule:
    - cron: "0 7 * * *" # run once daily - will not create a new release if the current HEAD already has a version tag
  workflow_dispatch:    # allow to manually trigger this workflow
jobs:
  release:
    if: github.repository_owner == 'joernio'
    concurrency: release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21
          cache: sbt
      - uses: sbt/setup-sbt@v1
      - run: sudo apt update && sudo apt install -y gnupg
      - run: echo $PGP_SECRET | base64 --decode | gpg --batch --import
        env:
          PGP_SECRET: ${{ secrets.PGP_SECRET }}
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 2.7
      - name: Install Bundler
        run: gem install bundler -v 2.4.22
      - name: Delete `.rustup` directory
        run: rm -rf /home/<USER>/.rustup # to save disk space
        if: runner.os == 'Linux'
      - name: Delete `.cargo` directory # to save disk space
        run: rm -rf /home/<USER>/.cargo
        if: runner.os == 'Linux'
      - run: sbt scalafmtCheck test
      - run: ./testDistro.sh
      - run: |
          cd joern-cli/target/universal/stage
          ./schema-extender/test.sh
          cd -
      - run: sbt ciReleaseSkipIfAlreadyReleased ciReleaseTagNextVersion ciReleaseSonatype createDistribution
        env:
          SONATYPE_PASSWORD: ${{ secrets.SONATYPE_PASSWORD }}
          SONATYPE_USERNAME: ${{ secrets.SONATYPE_USERNAME }}
      - name: get tagged version
        run: echo "LATEST_TAG=$(git describe --tags --abbrev=0)" >> $GITHUB_ENV
      - name: Trigger release-github workflow
        run: |
          curl -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            https://api.github.com/repos/${{ github.repository }}/actions/workflows/release-github.yml/dispatches \
            -d '{"ref":"${{ env.LATEST_TAG }}"}'
