name: master
on:
  workflow_dispatch:    # allow to manually trigger this workflow
  push:
    branches: [master, main]
    tags: ["*"]
jobs:
  test:
    concurrency: master
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21
          cache: sbt
      - uses: sbt/setup-sbt@v1
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 2.7
      - name: Install Bundler
        run: gem install bundler -v 2.4.22
      - name: Delete `.rustup` directory
        run: rm -rf /home/<USER>/.rustup # to save disk space
        if: runner.os == 'Linux'
      - name: Delete `.cargo` directory # to save disk space
        run: rm -rf /home/<USER>/.cargo
        if: runner.os == 'Linux'
      - uses: actions/cache@v4
        with:
          path: |
            ~/.sbt
            ~/.coursier
          key: ${{ runner.os }}-sbt-${{ hashfiles('**/build.sbt') }}
      - run: sbt scalafmtCheck test
      - run: ./testDistro.sh
      - run: |
          cd joern-cli/target/universal/stage
          ./schema-extender/test.sh
          cd -
