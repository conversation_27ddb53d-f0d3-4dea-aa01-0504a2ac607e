name: Upload Container image

on:
  schedule:
    - cron: "0 8 * * *" # run once daily
  workflow_dispatch:    # allow to manually trigger this workflow

env:
  REGISTRY: ghcr.io

jobs:
  deploy:
    if: github.repository_owner == 'joernio'
    runs-on: ubuntu-latest
    continue-on-error: true
    permissions:
      contents: write
      packages: write
    steps:
      - uses: actions/checkout@v4
      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Log in to the Container registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: |
            ghcr.io/joernio/joern

      - name: Build and push Docker images
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ci/Dockerfile.alma
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=joern
          cache-to: type=gha,mode=max,scope=joern

      - name: Extract metadata (tags, labels) for Docker
        id: meta3
        uses: docker/metadata-action@v4
        with:
          images: |
            ghcr.io/joernio/joern-slim

      - name: Build and push Docker images
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ci/Dockerfile.slim
          platforms: linux/amd64
          push: true
          tags: ${{ steps.meta3.outputs.tags }}
          labels: ${{ steps.meta3.outputs.labels }}
          cache-from: type=gha,scope=joern-slim
          cache-to: type=gha,mode=max,scope=joern-slim

      - name: Extract metadata (tags, labels) for Docker
        id: meta2
        uses: docker/metadata-action@v4
        with:
          images: |
            ghcr.io/joernio/joern-alma8

      - name: Build and push Docker images
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ci/Dockerfile.alma8
          platforms: linux/amd64
          push: true
          tags: ${{ steps.meta2.outputs.tags }}
          labels: ${{ steps.meta2.outputs.labels }}
          cache-from: type=gha,scope=joern-alma8
          cache-to: type=gha,mode=max,scope=joern-alma8
