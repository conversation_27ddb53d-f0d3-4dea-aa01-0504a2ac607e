name: release to github
on:
  workflow_dispatch: # triggered by 'release' workflow via github REST api
jobs:
  release-github:
    concurrency: release
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set up JDK
        uses: actions/setup-java@v4
        with:
          distribution: temurin
          java-version: 21
          cache: sbt
      - uses: sbt/setup-sbt@v1
      - name: Set up Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 2.7
      - name: Install Bundler
        run: gem install bundler -v 2.4.22
      - name: Delete `.rustup` directory
        run: rm -rf /home/<USER>/.rustup # to save disk space
        if: runner.os == 'Linux'
      - name: Delete `.cargo` directory # to save disk space
        run: rm -rf /home/<USER>/.cargo
        if: runner.os == 'Linux'
      - run: sbt stage createDistribution
      - run: |
          cd joern-cli/target/universal/stage
          ./schema-extender/test.sh
          cd -
      - run: sha512sum target/joern-cli.zip > target/joern-cli.zip.sha512
      - run: sbt "querydb/runMain io.joern.dumpq.Main"
      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ github.ref_name }}
          release_name: ${{ github.ref_name }}
          draft: false
          prerelease: false
      - name: Upload joern-install.sh
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./joern-install.sh
          asset_name: joern-install.sh
          asset_content_type: text/plain
      - name: Upload joern-cli.zip
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: target/joern-cli.zip
          asset_name: joern-cli.zip
          asset_content_type: application/zip
      - name: Upload joern-cli.zip.sha512
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: target/joern-cli.zip.sha512
          asset_name: joern-cli.zip.sha512
          asset_content_type: text/plain
      - name: Upload querydb.zip
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: querydb/target/querydb.zip
          asset_name: querydb.zip
          asset_content_type: application/zip
      - name: Upload querydb.json
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: /tmp/querydb.json
          asset_name: querydb.json
          asset_content_type: application/json
